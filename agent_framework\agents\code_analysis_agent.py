"""
Specialized agent for code analysis tasks.
"""

import ast
import logging
from typing import Any, Dict, List, Optional

from ..core.multi_agent_types import AgentCapability
from ..core.types import Task, TaskResult, TaskStatus
from ..core.config import AgentRoleConfig
from .base_agent import BaseAgent


class CodeAnalysisAgent(BaseAgent):
    """
    Specialized agent for code analysis tasks.
    
    Capabilities:
    - Static code analysis
    - Code quality assessment
    - Complexity analysis
    - Security vulnerability detection
    - Code pattern recognition
    """
    
    def __init__(self, role_config: Optional[AgentRoleConfig] = None, **kwargs):
        """Initialize the code analysis agent."""
        if not role_config:
            role_config = self._create_default_config()
        
        super().__init__(role_config, **kwargs)
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def _create_default_config(cls) -> AgentRoleConfig:
        """Create default configuration for code analysis agent."""
        return AgentRoleConfig(
            name="code_analysis_agent",
            description="Specialized agent for code analysis and quality assessment",
            system_message="""You are a specialized code analysis agent. Your role is to:

1. Analyze code for quality, complexity, and maintainability
2. Identify potential bugs, security vulnerabilities, and performance issues
3. Suggest improvements and best practices
4. Provide detailed reports on code structure and patterns
5. Assess code compliance with standards and conventions

You have access to various code analysis tools and can perform both static and dynamic analysis.
Always provide specific, actionable feedback with examples and recommendations.""",
            capabilities=[
                AgentCapability.CODE_ANALYSIS.value,
                AgentCapability.ERROR_DETECTION.value,
                AgentCapability.FILE_OPERATIONS.value
            ],
            mcp_servers=["filesystem", "code_analysis"],
            max_concurrent_tasks=3,
            priority=2
        )
    
    async def analyze_code_file(self, file_path: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Analyze a specific code file.
        
        Args:
            file_path: Path to the code file
            analysis_type: Type of analysis (basic, comprehensive, security)
            
        Returns:
            Analysis results
        """
        task = Task(
            name=f"analyze_code_file_{analysis_type}",
            description=f"Analyze code file {file_path} with {analysis_type} analysis",
            task_type="code_analysis",
            parameters={
                "file_path": file_path,
                "analysis_type": analysis_type
            }
        )
        
        result = await self.execute_task(task)
        return result.result if result.status == TaskStatus.COMPLETED else {}
    
    async def analyze_code_quality(self, code_content: str, language: str = "python") -> Dict[str, Any]:
        """
        Analyze code quality metrics.
        
        Args:
            code_content: The code content to analyze
            language: Programming language
            
        Returns:
            Quality analysis results
        """
        task = Task(
            name="analyze_code_quality",
            description=f"Analyze code quality for {language} code",
            task_type="code_analysis",
            parameters={
                "code_content": code_content,
                "language": language,
                "metrics": ["complexity", "maintainability", "readability", "duplication"]
            }
        )
        
        result = await self.execute_task(task)
        return result.result if result.status == TaskStatus.COMPLETED else {}
    
    async def detect_security_vulnerabilities(self, code_content: str, language: str = "python") -> List[Dict[str, Any]]:
        """
        Detect security vulnerabilities in code.
        
        Args:
            code_content: The code content to analyze
            language: Programming language
            
        Returns:
            List of detected vulnerabilities
        """
        task = Task(
            name="detect_security_vulnerabilities",
            description=f"Detect security vulnerabilities in {language} code",
            task_type="security_analysis",
            parameters={
                "code_content": code_content,
                "language": language,
                "vulnerability_types": ["injection", "xss", "csrf", "auth", "crypto"]
            }
        )
        
        result = await self.execute_task(task)
        return result.result if result.status == TaskStatus.COMPLETED else []
    
    async def analyze_code_complexity(self, code_content: str, language: str = "python") -> Dict[str, Any]:
        """
        Analyze code complexity metrics.
        
        Args:
            code_content: The code content to analyze
            language: Programming language
            
        Returns:
            Complexity analysis results
        """
        if language.lower() == "python":
            return await self._analyze_python_complexity(code_content)
        else:
            # Use general analysis for other languages
            task = Task(
                name="analyze_code_complexity",
                description=f"Analyze complexity for {language} code",
                task_type="complexity_analysis",
                parameters={
                    "code_content": code_content,
                    "language": language
                }
            )
            
            result = await self.execute_task(task)
            return result.result if result.status == TaskStatus.COMPLETED else {}
    
    async def identify_code_patterns(self, code_content: str, language: str = "python") -> List[Dict[str, Any]]:
        """
        Identify design patterns and anti-patterns in code.
        
        Args:
            code_content: The code content to analyze
            language: Programming language
            
        Returns:
            List of identified patterns
        """
        task = Task(
            name="identify_code_patterns",
            description=f"Identify patterns in {language} code",
            task_type="pattern_analysis",
            parameters={
                "code_content": code_content,
                "language": language,
                "pattern_types": ["design_patterns", "anti_patterns", "code_smells"]
            }
        )
        
        result = await self.execute_task(task)
        return result.result if result.status == TaskStatus.COMPLETED else []
    
    async def generate_code_report(self, analysis_results: Dict[str, Any]) -> str:
        """
        Generate a comprehensive code analysis report.
        
        Args:
            analysis_results: Combined analysis results
            
        Returns:
            Formatted report
        """
        task = Task(
            name="generate_code_report",
            description="Generate comprehensive code analysis report",
            task_type="report_generation",
            parameters={
                "analysis_results": analysis_results,
                "report_format": "markdown",
                "include_recommendations": True
            }
        )
        
        result = await self.execute_task(task)
        return result.result if result.status == TaskStatus.COMPLETED else ""
    
    async def _analyze_python_complexity(self, code_content: str) -> Dict[str, Any]:
        """Analyze Python code complexity using AST."""
        try:
            tree = ast.parse(code_content)
            
            complexity_metrics = {
                "cyclomatic_complexity": self._calculate_cyclomatic_complexity(tree),
                "cognitive_complexity": self._calculate_cognitive_complexity(tree),
                "lines_of_code": len(code_content.splitlines()),
                "function_count": self._count_functions(tree),
                "class_count": self._count_classes(tree),
                "max_nesting_depth": self._calculate_max_nesting_depth(tree)
            }
            
            return complexity_metrics
            
        except SyntaxError as e:
            self.logger.error(f"Syntax error in Python code: {e}")
            return {"error": f"Syntax error: {e}"}
        except Exception as e:
            self.logger.error(f"Error analyzing Python complexity: {e}")
            return {"error": f"Analysis error: {e}"}
    
    def _calculate_cyclomatic_complexity(self, tree: ast.AST) -> int:
        """Calculate cyclomatic complexity."""
        complexity = 1  # Base complexity
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(node, ast.comprehension):
                complexity += 1
        
        return complexity
    
    def _calculate_cognitive_complexity(self, tree: ast.AST) -> int:
        """Calculate cognitive complexity."""
        # Simplified cognitive complexity calculation
        complexity = 0
        nesting_level = 0
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1 + nesting_level
                nesting_level += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1 + nesting_level
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _count_functions(self, tree: ast.AST) -> int:
        """Count the number of functions."""
        return len([node for node in ast.walk(tree) 
                   if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))])
    
    def _count_classes(self, tree: ast.AST) -> int:
        """Count the number of classes."""
        return len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
    
    def _calculate_max_nesting_depth(self, tree: ast.AST) -> int:
        """Calculate maximum nesting depth."""
        max_depth = 0
        
        def calculate_depth(node, current_depth=0):
            nonlocal max_depth
            max_depth = max(max_depth, current_depth)
            
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor, 
                                ast.With, ast.AsyncWith, ast.Try, ast.FunctionDef, 
                                ast.AsyncFunctionDef, ast.ClassDef)):
                current_depth += 1
            
            for child in ast.iter_child_nodes(node):
                calculate_depth(child, current_depth)
        
        calculate_depth(tree)
        return max_depth
    
    def _prepare_task_prompt(self, task: Task) -> str:
        """Prepare a specialized prompt for code analysis tasks."""
        base_prompt = super()._prepare_task_prompt(task)
        
        if task.task_type == "code_analysis":
            base_prompt += "\nPlease provide a detailed code analysis including:\n"
            base_prompt += "- Code quality assessment\n"
            base_prompt += "- Potential issues and improvements\n"
            base_prompt += "- Best practice recommendations\n"
            base_prompt += "- Specific examples with line numbers if applicable\n"
        elif task.task_type == "security_analysis":
            base_prompt += "\nFocus on security analysis including:\n"
            base_prompt += "- Potential security vulnerabilities\n"
            base_prompt += "- Risk assessment and severity levels\n"
            base_prompt += "- Mitigation strategies\n"
            base_prompt += "- Secure coding recommendations\n"
        elif task.task_type == "complexity_analysis":
            base_prompt += "\nAnalyze code complexity including:\n"
            base_prompt += "- Cyclomatic and cognitive complexity\n"
            base_prompt += "- Code structure and organization\n"
            base_prompt += "- Refactoring suggestions\n"
            base_prompt += "- Maintainability assessment\n"
        
        return base_prompt
