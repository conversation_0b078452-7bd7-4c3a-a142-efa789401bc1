"""
Comprehensive logging system for multi-agent interactions.
"""

import json
import logging
import logging.handlers
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
from uuid import UUID

from ..core.multi_agent_types import (
    AgentInfo, AgentStatus, MultiAgentEvent, 
    AgentCommunicationMessage, TaskDelegationRequest, TaskDelegationResponse
)
from ..core.types import Task, TaskResult


class MultiAgentLogger:
    """
    Specialized logger for multi-agent system interactions.
    
    Provides structured logging for:
    - Agent lifecycle events
    - Task delegation and execution
    - Inter-agent communication
    - Performance metrics
    - Error tracking and debugging
    """
    
    def __init__(self, 
                 log_dir: str = "logs",
                 log_level: str = "INFO",
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5):
        """
        Initialize the multi-agent logger.
        
        Args:
            log_dir: Directory for log files
            log_level: Logging level
            max_file_size: Maximum size per log file
            backup_count: Number of backup files to keep
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Create specialized loggers
        self._setup_loggers(log_level, max_file_size, backup_count)
        
        # Event tracking
        self._event_sequence = 0
        self._session_id = str(int(time.time()))
    
    def _setup_loggers(self, log_level: str, max_file_size: int, backup_count: int) -> None:
        """Setup specialized loggers for different aspects."""
        
        # Main multi-agent logger
        self.main_logger = self._create_logger(
            "multi_agent",
            self.log_dir / "multi_agent.log",
            log_level,
            max_file_size,
            backup_count
        )
        
        # Agent lifecycle logger
        self.agent_logger = self._create_logger(
            "agent_lifecycle",
            self.log_dir / "agent_lifecycle.log",
            log_level,
            max_file_size,
            backup_count
        )
        
        # Task execution logger
        self.task_logger = self._create_logger(
            "task_execution",
            self.log_dir / "task_execution.log",
            log_level,
            max_file_size,
            backup_count
        )
        
        # Communication logger
        self.comm_logger = self._create_logger(
            "communication",
            self.log_dir / "communication.log",
            log_level,
            max_file_size,
            backup_count
        )
        
        # Performance logger
        self.perf_logger = self._create_logger(
            "performance",
            self.log_dir / "performance.log",
            log_level,
            max_file_size,
            backup_count
        )
        
        # Error logger
        self.error_logger = self._create_logger(
            "errors",
            self.log_dir / "errors.log",
            "ERROR",
            max_file_size,
            backup_count
        )
    
    def _create_logger(self, 
                      name: str, 
                      log_file: Path, 
                      level: str,
                      max_file_size: int,
                      backup_count: int) -> logging.Logger:
        """Create a configured logger."""
        logger = logging.getLogger(f"multi_agent.{name}")
        logger.setLevel(getattr(logging, level.upper()))
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count
        )
        
        # JSON formatter for structured logging
        formatter = JsonFormatter()
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.propagate = False
        
        return logger
    
    def log_agent_event(self, 
                       agent_id: UUID,
                       event_type: str,
                       event_data: Dict[str, Any],
                       agent_info: Optional[AgentInfo] = None) -> None:
        """
        Log an agent lifecycle event.
        
        Args:
            agent_id: ID of the agent
            event_type: Type of event (initialized, started, stopped, etc.)
            event_data: Additional event data
            agent_info: Optional agent information
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self._session_id,
            "sequence": self._get_next_sequence(),
            "event_type": "agent_event",
            "agent_id": str(agent_id),
            "agent_event_type": event_type,
            "event_data": event_data
        }
        
        if agent_info:
            log_entry["agent_info"] = {
                "name": agent_info.name,
                "role": agent_info.role,
                "status": agent_info.status.value,
                "capabilities": [cap.value for cap in agent_info.capabilities],
                "priority": agent_info.priority
            }
        
        self.agent_logger.info(json.dumps(log_entry))
    
    def log_task_delegation(self, 
                           request: TaskDelegationRequest,
                           response: TaskDelegationResponse) -> None:
        """
        Log task delegation event.
        
        Args:
            request: Task delegation request
            response: Task delegation response
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self._session_id,
            "sequence": self._get_next_sequence(),
            "event_type": "task_delegation",
            "task_id": str(request.task.id),
            "task_name": request.task.name,
            "task_type": request.task.task_type,
            "required_capabilities": [cap.value for cap in request.required_capabilities],
            "preferred_agent": str(request.preferred_agent_id) if request.preferred_agent_id else None,
            "delegation_success": response.success,
            "assigned_agent": str(response.assigned_agent_id) if response.assigned_agent_id else None,
            "estimated_completion_time": response.estimated_completion_time,
            "error_message": response.error_message
        }
        
        self.task_logger.info(json.dumps(log_entry))
    
    def log_task_execution(self, 
                          task: Task,
                          agent_id: UUID,
                          result: TaskResult,
                          execution_details: Optional[Dict[str, Any]] = None) -> None:
        """
        Log task execution event.
        
        Args:
            task: The executed task
            agent_id: ID of the executing agent
            result: Task execution result
            execution_details: Additional execution details
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self._session_id,
            "sequence": self._get_next_sequence(),
            "event_type": "task_execution",
            "task_id": str(task.id),
            "task_name": task.name,
            "task_type": task.task_type,
            "agent_id": str(agent_id),
            "execution_status": result.status.value,
            "execution_time": result.execution_time,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": result.completed_at.isoformat() if result.completed_at else None,
            "error": result.error,
            "retry_count": task.retry_count
        }
        
        if execution_details:
            log_entry["execution_details"] = execution_details
        
        self.task_logger.info(json.dumps(log_entry))
    
    def log_agent_communication(self, 
                              message: AgentCommunicationMessage,
                              direction: str = "sent") -> None:
        """
        Log inter-agent communication.
        
        Args:
            message: Communication message
            direction: Direction of message (sent/received)
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self._session_id,
            "sequence": self._get_next_sequence(),
            "event_type": "agent_communication",
            "message_id": str(message.id),
            "sender_id": str(message.sender_id),
            "recipient_id": str(message.recipient_id) if message.recipient_id else "broadcast",
            "message_type": message.message_type,
            "direction": direction,
            "requires_response": message.requires_response,
            "correlation_id": str(message.correlation_id) if message.correlation_id else None,
            "content_size": len(json.dumps(message.content))
        }
        
        self.comm_logger.info(json.dumps(log_entry))
    
    def log_performance_metric(self, 
                             metric_name: str,
                             metric_value: float,
                             metric_unit: str,
                             context: Optional[Dict[str, Any]] = None) -> None:
        """
        Log performance metric.
        
        Args:
            metric_name: Name of the metric
            metric_value: Value of the metric
            metric_unit: Unit of measurement
            context: Additional context
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self._session_id,
            "sequence": self._get_next_sequence(),
            "event_type": "performance_metric",
            "metric_name": metric_name,
            "metric_value": metric_value,
            "metric_unit": metric_unit
        }
        
        if context:
            log_entry["context"] = context
        
        self.perf_logger.info(json.dumps(log_entry))
    
    def log_coordination_event(self, 
                             coordination_id: UUID,
                             event_type: str,
                             event_data: Dict[str, Any]) -> None:
        """
        Log coordination event.
        
        Args:
            coordination_id: ID of the coordination
            event_type: Type of coordination event
            event_data: Event data
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self._session_id,
            "sequence": self._get_next_sequence(),
            "event_type": "coordination_event",
            "coordination_id": str(coordination_id),
            "coordination_event_type": event_type,
            "event_data": event_data
        }
        
        self.main_logger.info(json.dumps(log_entry))
    
    def log_error(self, 
                 error: Exception,
                 context: Dict[str, Any],
                 agent_id: Optional[UUID] = None,
                 task_id: Optional[UUID] = None) -> None:
        """
        Log error with context.
        
        Args:
            error: The exception that occurred
            context: Error context
            agent_id: Optional agent ID
            task_id: Optional task ID
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self._session_id,
            "sequence": self._get_next_sequence(),
            "event_type": "error",
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context
        }
        
        if agent_id:
            log_entry["agent_id"] = str(agent_id)
        
        if task_id:
            log_entry["task_id"] = str(task_id)
        
        self.error_logger.error(json.dumps(log_entry))
    
    def log_system_event(self, 
                        event_type: str,
                        event_data: Dict[str, Any]) -> None:
        """
        Log system-level event.
        
        Args:
            event_type: Type of system event
            event_data: Event data
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "session_id": self._session_id,
            "sequence": self._get_next_sequence(),
            "event_type": "system_event",
            "system_event_type": event_type,
            "event_data": event_data
        }
        
        self.main_logger.info(json.dumps(log_entry))
    
    def _get_next_sequence(self) -> int:
        """Get next sequence number for events."""
        self._event_sequence += 1
        return self._event_sequence
    
    def get_session_id(self) -> str:
        """Get current session ID."""
        return self._session_id
    
    def flush_all_logs(self) -> None:
        """Flush all log handlers."""
        for logger in [self.main_logger, self.agent_logger, self.task_logger,
                      self.comm_logger, self.perf_logger, self.error_logger]:
            for handler in logger.handlers:
                handler.flush()


class JsonFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        try:
            # Try to parse the message as JSON
            log_data = json.loads(record.getMessage())
            return json.dumps(log_data, ensure_ascii=False)
        except (json.JSONDecodeError, ValueError):
            # Fallback to standard formatting
            log_data = {
                "timestamp": datetime.fromtimestamp(record.created).isoformat(),
                "level": record.levelname,
                "logger": record.name,
                "message": record.getMessage()
            }
            
            if record.exc_info:
                log_data["exception"] = self.formatException(record.exc_info)
            
            return json.dumps(log_data, ensure_ascii=False)
