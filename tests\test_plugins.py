"""
Tests for the plugin system components.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, patch

from agent_framework.core.types import PluginRequest, PluginResponse, PluginCapability
from agent_framework.plugins.registry import PluginRegistry, PluginMetadata
from agent_framework.plugins.loader import <PERSON>lugin<PERSON>oader
from agent_framework.plugins.manager import PluginManager


class TestPluginRegistry:
    """Test cases for PluginRegistry."""

    def test_registry_initialization(self):
        """Test registry initialization."""
        registry = PluginRegistry()
        assert len(registry.get_all_plugins()) == 0
        assert registry.get_registry_stats()["total_plugins"] == 0

    def test_register_plugin(self):
        """Test plugin registration."""
        registry = PluginRegistry()

        metadata = PluginMetadata(
            name="test_plugin",
            version="1.0.0",
            description="A test plugin",
            file_path="/test/path.py",
            class_name="TestPlugin"
        )

        registry.register_plugin(metadata)

        assert len(registry.get_all_plugins()) == 1
        assert registry.get_plugin("test_plugin") == metadata
        assert registry.get_registry_stats()["total_plugins"] == 1

    def test_register_duplicate_plugin(self):
        """Test registering a plugin with the same name."""
        registry = PluginRegistry()

        metadata1 = PluginMetadata(
            name="test_plugin",
            version="1.0.0",
            description="First version",
            file_path="/test/path1.py",
            class_name="TestPlugin1"
        )

        metadata2 = PluginMetadata(
            name="test_plugin",
            version="2.0.0",
            description="Second version",
            file_path="/test/path2.py",
            class_name="TestPlugin2"
        )

        registry.register_plugin(metadata1)
        registry.register_plugin(metadata2)  # Should replace the first one

        assert len(registry.get_all_plugins()) == 1
        assert registry.get_plugin("test_plugin").version == "2.0.0"

    def test_unregister_plugin(self):
        """Test plugin unregistration."""
        registry = PluginRegistry()

        metadata = PluginMetadata(
            name="test_plugin",
            version="1.0.0",
            description="A test plugin",
            file_path="/test/path.py",
            class_name="TestPlugin"
        )

        registry.register_plugin(metadata)
        assert len(registry.get_all_plugins()) == 1

        registry.unregister_plugin("test_plugin")
        assert len(registry.get_all_plugins()) == 0
        assert registry.get_plugin("test_plugin") is None

    def test_enable_disable_plugin(self):
        """Test enabling and disabling plugins."""
        registry = PluginRegistry()

        metadata = PluginMetadata(
            name="test_plugin",
            version="1.0.0",
            description="A test plugin",
            file_path="/test/path.py",
            class_name="TestPlugin"
        )

        registry.register_plugin(metadata)
        assert metadata.is_enabled

        registry.disable_plugin("test_plugin")
        assert not metadata.is_enabled
        assert len(registry.get_enabled_plugins()) == 0

        registry.enable_plugin("test_plugin")
        assert metadata.is_enabled
        assert len(registry.get_enabled_plugins()) == 1

    def test_dependency_management(self):
        """Test plugin dependency management."""
        registry = PluginRegistry()

        # Plugin with dependencies
        metadata = PluginMetadata(
            name="dependent_plugin",
            version="1.0.0",
            description="A plugin with dependencies",
            dependencies=["base_plugin", "utils_plugin"],
            file_path="/test/path.py",
            class_name="DependentPlugin"
        )

        registry.register_plugin(metadata)

        deps = registry.get_plugin_dependencies("dependent_plugin")
        assert "base_plugin" in deps
        assert "utils_plugin" in deps

        # Test dependency validation
        errors = registry.validate_dependencies("dependent_plugin")
        assert len(errors) == 2  # Both dependencies are missing
        assert "Missing dependency: base_plugin" in errors
        assert "Missing dependency: utils_plugin" in errors

    def test_dependency_ordering(self):
        """Test dependency ordering."""
        registry = PluginRegistry()

        # Register plugins with dependencies
        base_metadata = PluginMetadata(
            name="base_plugin",
            version="1.0.0",
            description="Base plugin",
            file_path="/test/base.py",
            class_name="BasePlugin"
        )

        dependent_metadata = PluginMetadata(
            name="dependent_plugin",
            version="1.0.0",
            description="Dependent plugin",
            dependencies=["base_plugin"],
            file_path="/test/dependent.py",
            class_name="DependentPlugin"
        )

        registry.register_plugin(base_metadata)
        registry.register_plugin(dependent_metadata)

        # Test dependency ordering
        ordered = registry.get_dependency_order(["dependent_plugin", "base_plugin"])
        assert ordered.index("base_plugin") < ordered.index("dependent_plugin")

    def test_capability_indexing(self):
        """Test capability indexing."""
        registry = PluginRegistry()

        capability = PluginCapability(
            name="test_capability",
            description="A test capability",
            input_schema={"type": "object"},
            output_schema={"type": "object"}
        )

        metadata = PluginMetadata(
            name="test_plugin",
            version="1.0.0",
            description="A test plugin",
            capabilities=[capability],
            file_path="/test/path.py",
            class_name="TestPlugin"
        )

        registry.register_plugin(metadata)

        plugins_with_capability = registry.get_plugins_by_capability("test_capability")
        assert len(plugins_with_capability) == 1
        assert plugins_with_capability[0].name == "test_plugin"


class TestPluginLoader:
    """Test cases for PluginLoader."""

    @pytest.mark.asyncio
    async def test_loader_initialization(self):
        """Test loader initialization."""
        registry = PluginRegistry()
        loader = PluginLoader(registry)

        assert loader.registry == registry
        assert len(loader.get_loaded_plugins()) == 0

    @pytest.mark.asyncio
    async def test_discover_plugins(self, temp_plugin_dir):
        """Test plugin discovery."""
        registry = PluginRegistry()
        loader = PluginLoader(registry)

        discovered = await loader.discover_plugins([temp_plugin_dir])

        assert len(discovered) == 1
        assert discovered[0].name == "test_plugin"
        assert discovered[0].version == "1.0.0"

    @pytest.mark.asyncio
    async def test_load_plugin(self, temp_plugin_dir):
        """Test plugin loading."""
        registry = PluginRegistry()
        loader = PluginLoader(registry)

        # First discover the plugin
        discovered = await loader.discover_plugins([temp_plugin_dir])
        registry.register_plugin(discovered[0])

        # Then load it
        plugin_instance = await loader.load_plugin("test_plugin")

        assert plugin_instance is not None
        assert plugin_instance.name == "test_plugin"
        assert loader.is_plugin_loaded("test_plugin")

    @pytest.mark.asyncio
    async def test_unload_plugin(self, temp_plugin_dir):
        """Test plugin unloading."""
        registry = PluginRegistry()
        loader = PluginLoader(registry)

        # Discover and load plugin
        discovered = await loader.discover_plugins([temp_plugin_dir])
        registry.register_plugin(discovered[0])
        await loader.load_plugin("test_plugin")

        assert loader.is_plugin_loaded("test_plugin")

        # Unload plugin
        await loader.unload_plugin("test_plugin")

        assert not loader.is_plugin_loaded("test_plugin")

    @pytest.mark.asyncio
    async def test_reload_plugin(self, temp_plugin_dir):
        """Test plugin reloading."""
        registry = PluginRegistry()
        loader = PluginLoader(registry)

        # Discover and load plugin
        discovered = await loader.discover_plugins([temp_plugin_dir])
        registry.register_plugin(discovered[0])

        plugin1 = await loader.load_plugin("test_plugin")
        plugin2 = await loader.reload_plugin("test_plugin")

        # Should be different instances
        assert plugin1 is not plugin2
        assert loader.is_plugin_loaded("test_plugin")


class TestPluginManager:
    """Test cases for PluginManager."""

    @pytest.mark.asyncio
    async def test_manager_initialization(self, plugin_manager):
        """Test manager initialization."""
        assert plugin_manager.is_initialized
        assert plugin_manager.registry is not None
        assert plugin_manager.loader is not None

    @pytest.mark.asyncio
    async def test_plugin_discovery(self, plugin_manager, temp_plugin_dir):
        """Test plugin discovery through manager."""
        # Update config to include temp directory
        plugin_manager.config.plugins.plugin_directories = [temp_plugin_dir]

        await plugin_manager.discover_plugins()

        stats = plugin_manager.registry.get_registry_stats()
        assert stats["total_plugins"] == 1

    @pytest.mark.asyncio
    async def test_load_plugin_through_manager(self, plugin_manager, mock_plugin):
        """Test loading plugin through manager."""
        # Register mock plugin manually
        metadata = PluginMetadata(
            name=mock_plugin.name,
            version=mock_plugin.version,
            description="Mock plugin",
            file_path="mock.py",
            class_name="MockPlugin"
        )
        plugin_manager.registry.register_plugin(metadata)

        # Mock the loader to return our mock plugin
        with patch.object(plugin_manager.loader, 'load_plugin', return_value=mock_plugin):
            loaded_plugin = await plugin_manager.load_plugin(mock_plugin.name)
            assert loaded_plugin == mock_plugin

    @pytest.mark.asyncio
    async def test_execute_plugin_request(self, plugin_manager, mock_plugin):
        """Test executing plugin request through manager."""
        # Setup mock plugin
        metadata = PluginMetadata(
            name=mock_plugin.name,
            version=mock_plugin.version,
            description="Mock plugin",
            file_path="mock.py",
            class_name="MockPlugin"
        )
        plugin_manager.registry.register_plugin(metadata)

        # Initialize mock plugin
        await mock_plugin.initialize({})

        # Mock the loader
        with patch.object(plugin_manager.loader, 'load_plugin', return_value=mock_plugin):
            with patch.object(plugin_manager.loader, 'is_plugin_loaded', return_value=True):
                with patch.object(plugin_manager.loader, 'get_loaded_plugins', return_value={mock_plugin.name: mock_plugin}):

                    request = PluginRequest(
                        capability="test_capability",
                        parameters={"test": "value"}
                    )

                    response = await plugin_manager.execute_plugin_request(mock_plugin.name, request)

                    assert response.success
                    assert "Mock plugin executed successfully" in str(response.result)

    @pytest.mark.asyncio
    async def test_get_plugin_capabilities(self, plugin_manager, mock_plugin):
        """Test getting plugin capabilities."""
        # Setup mock plugin
        metadata = PluginMetadata(
            name=mock_plugin.name,
            version=mock_plugin.version,
            description="Mock plugin",
            file_path="mock.py",
            class_name="MockPlugin"
        )
        plugin_manager.registry.register_plugin(metadata)

        await mock_plugin.initialize({})

        with patch.object(plugin_manager.loader, 'load_plugin', return_value=mock_plugin):
            with patch.object(plugin_manager.loader, 'is_plugin_loaded', return_value=True):
                with patch.object(plugin_manager.loader, 'get_loaded_plugins', return_value={mock_plugin.name: mock_plugin}):

                    capabilities = await plugin_manager.get_plugin_capabilities(mock_plugin.name)

                    assert len(capabilities) == 1
                    assert capabilities[0].name == "test_capability"

    @pytest.mark.asyncio
    async def test_find_plugins_by_capability(self, plugin_manager, mock_plugin):
        """Test finding plugins by capability."""
        await mock_plugin.initialize({})

        with patch.object(plugin_manager.loader, 'get_loaded_plugins', return_value={mock_plugin.name: mock_plugin}):
            plugins = await plugin_manager.find_plugins_by_capability("test_capability")

            assert mock_plugin.name in plugins

    @pytest.mark.asyncio
    async def test_get_plugin_status(self, plugin_manager):
        """Test getting plugin status."""
        status = await plugin_manager.get_plugin_status()

        assert "registry_stats" in status
        assert "loaded_count" in status
        assert "plugin_details" in status
        assert isinstance(status["registry_stats"]["total_plugins"], int)
        assert isinstance(status["loaded_count"], int)