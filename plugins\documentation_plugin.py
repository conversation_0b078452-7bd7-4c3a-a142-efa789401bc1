"""
Documentation Plugin

Provides comprehensive documentation generation capabilities including
docstring generation, API documentation, and code comments.
"""

import ast
import re
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from textwrap import dedent

from agent_framework.core.types import (
    PluginInterface, PluginRequest, PluginResponse, PluginCapability
)


class DocumentationPlugin(PluginInterface):
    """
    Plugin for generating comprehensive documentation.

    Provides capabilities for:
    - Docstring generation for functions and classes
    - API documentation generation
    - Code comment generation
    - README file generation
    - Type hint documentation
    """

    PLUGIN_NAME = "documentation"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Generates comprehensive documentation for code"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    PLUGIN_DEPENDENCIES = []

    def __init__(self):
        """Initialize the documentation plugin."""
        self._is_initialized = False
        self._config = {}
        self._docstring_templates = {}
        self._load_docstring_templates()

    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME

    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self.PLUGIN_VERSION

    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration."""
        self._config = config
        self._is_initialized = True

    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )

        start_time = time.time()

        try:
            capability = request.capability
            parameters = request.parameters

            if capability == "generate_docstrings":
                result = await self._generate_docstrings(parameters)
            elif capability == "generate_api_docs":
                result = await self._generate_api_docs(parameters)
            elif capability == "generate_comments":
                result = await self._generate_comments(parameters)
            elif capability == "generate_readme":
                result = await self._generate_readme(parameters)
            elif capability == "document_types":
                result = await self._document_types(parameters)
            elif capability == "generate_changelog":
                result = await self._generate_changelog(parameters)
            else:
                return PluginResponse(
                    success=False,
                    error=f"Unknown capability: {capability}"
                )

            execution_time = time.time() - start_time

            return PluginResponse(
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={
                    "capability": capability,
                    "parameters": parameters
                }
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return PluginResponse(
                success=False,
                error=str(e),
                execution_time=execution_time
            )

    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return [
            PluginCapability(
                name="generate_docstrings",
                description="Generate docstrings for functions and classes",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Python code to document"},
                        "file_path": {"type": "string", "description": "Path to Python file"},
                        "style": {"type": "string", "enum": ["google", "numpy", "sphinx"], "default": "google"},
                        "include_examples": {"type": "boolean", "default": False}
                    },
                    "oneOf": [
                        {"required": ["code"]},
                        {"required": ["file_path"]}
                    ]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "documented_code": {"type": "string"},
                        "docstrings": {"type": "array"},
                        "coverage": {"type": "number"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="generate_api_docs",
                description="Generate API documentation",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "format": {"type": "string", "enum": ["markdown", "rst", "html"], "default": "markdown"},
                        "include_private": {"type": "boolean", "default": False}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "documentation": {"type": "string"},
                        "sections": {"type": "array"},
                        "toc": {"type": "string"}
                    }
                },
                supported_languages=["python"]
            ),
            PluginCapability(
                name="generate_comments",
                description="Generate inline comments for complex code",
                input_schema={
                    "type": "object",
                    "properties": {
                        "code": {"type": "string"},
                        "comment_style": {"type": "string", "enum": ["inline", "block"], "default": "inline"}
                    },
                    "required": ["code"]
                },
                output_schema={
                    "type": "object",
                    "properties": {
                        "commented_code": {"type": "string"},
                        "comment_count": {"type": "number"}
                    }
                },
                supported_languages=["python"]
            )
        ]

    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        self._is_initialized = False
        self._config.clear()
        self._docstring_templates.clear()

    def _load_docstring_templates(self) -> None:
        """Load docstring templates for different styles."""
        self._docstring_templates = {
            "google": {
                "function": '''"""{description}

Args:
{args}

Returns:
    {return_type}: {return_description}

Raises:
    {exceptions}

Example:
    {example}
"""''',
                "class": '''"""{description}

Attributes:
{attributes}

Example:
    {example}
"""'''
            },
            "numpy": {
                "function": '''"""{description}

Parameters
----------
{parameters}

Returns
-------
{return_type}
    {return_description}

Raises
------
{exceptions}

Examples
--------
{example}
"""''',
                "class": '''"""{description}

Attributes
----------
{attributes}

Examples
--------
{example}
"""'''
            },
            "sphinx": {
                "function": '''"""{description}

:param {param_list}
:type {param_types}
:returns: {return_description}
:rtype: {return_type}
:raises {exceptions}

.. code-block:: python

    {example}
"""''',
                "class": '''"""{description}

:ivar {attributes}

.. code-block:: python

    {example}
"""'''
            }
        }

    async def _generate_docstrings(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate docstrings for functions and classes."""
        code = await self._get_code(parameters)
        style = parameters.get("style", "google")
        include_examples = parameters.get("include_examples", False)

        try:
            tree = ast.parse(code)
            docstring_generator = DocstringGenerator(style, include_examples)
            docstring_generator.visit(tree)

            documented_code = docstring_generator.apply_docstrings(code)
            docstrings = docstring_generator.get_generated_docstrings()
            coverage = docstring_generator.calculate_coverage()

            return {
                "documented_code": documented_code,
                "docstrings": docstrings,
                "coverage": coverage,
                "metadata": {
                    "style": style,
                    "functions_documented": len([d for d in docstrings if d["type"] == "function"]),
                    "classes_documented": len([d for d in docstrings if d["type"] == "class"])
                }
            }

        except SyntaxError as e:
            raise ValueError(f"Syntax error in code: {e}")

    async def _generate_api_docs(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate API documentation."""
        code = parameters.get("code", "")
        format_type = parameters.get("format", "markdown")
        include_private = parameters.get("include_private", False)

        try:
            tree = ast.parse(code)
            api_generator = APIDocGenerator(format_type, include_private)
            api_generator.visit(tree)

            documentation = api_generator.generate_documentation()
            sections = api_generator.get_sections()
            toc = api_generator.generate_toc()

            return {
                "documentation": documentation,
                "sections": sections,
                "toc": toc,
                "metadata": {
                    "format": format_type,
                    "include_private": include_private,
                    "section_count": len(sections)
                }
            }

        except SyntaxError as e:
            raise ValueError(f"Syntax error in code: {e}")

    async def _generate_comments(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate inline comments for complex code."""
        code = parameters.get("code", "")
        comment_style = parameters.get("comment_style", "inline")

        try:
            tree = ast.parse(code)
            comment_generator = CommentGenerator(comment_style)
            comment_generator.visit(tree)

            commented_code = comment_generator.apply_comments(code)
            comment_count = comment_generator.get_comment_count()

            return {
                "commented_code": commented_code,
                "comment_count": comment_count,
                "metadata": {
                    "comment_style": comment_style,
                    "original_lines": len(code.split('\n')),
                    "commented_lines": len(commented_code.split('\n'))
                }
            }

        except SyntaxError as e:
            raise ValueError(f"Syntax error in code: {e}")

    async def _generate_readme(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate README file for a project."""
        project_name = parameters.get("project_name", "My Project")
        description = parameters.get("description", "A Python project")
        features = parameters.get("features", [])
        installation = parameters.get("installation", "pip install project")
        usage_example = parameters.get("usage_example", "")

        readme_content = self._create_readme_template(
            project_name, description, features, installation, usage_example
        )

        return {
            "readme_content": readme_content,
            "sections": ["Title", "Description", "Features", "Installation", "Usage", "Contributing", "License"],
            "metadata": {
                "project_name": project_name,
                "feature_count": len(features)
            }
        }

    async def _document_types(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Document type hints and annotations."""
        code = parameters.get("code", "")

        try:
            tree = ast.parse(code)
            type_documenter = TypeDocumenter()
            type_documenter.visit(tree)

            type_documentation = type_documenter.generate_type_docs()
            type_coverage = type_documenter.calculate_type_coverage()

            return {
                "type_documentation": type_documentation,
                "type_coverage": type_coverage,
                "metadata": {
                    "functions_with_types": type_documenter.functions_with_types,
                    "total_functions": type_documenter.total_functions
                }
            }

        except SyntaxError as e:
            raise ValueError(f"Syntax error in code: {e}")

    async def _generate_changelog(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Generate changelog from version history."""
        versions = parameters.get("versions", [])
        format_type = parameters.get("format", "markdown")

        changelog_content = self._create_changelog_template(versions, format_type)

        return {
            "changelog_content": changelog_content,
            "version_count": len(versions),
            "metadata": {
                "format": format_type,
                "latest_version": versions[0]["version"] if versions else "1.0.0"
            }
        }

    async def _get_code(self, parameters: Dict[str, Any]) -> str:
        """Get code from parameters (either direct code or file path)."""
        if "code" in parameters:
            return parameters["code"]
        elif "file_path" in parameters:
            file_path = Path(parameters["file_path"])
            if not file_path.exists():
                raise ValueError(f"File not found: {file_path}")
            return file_path.read_text(encoding='utf-8')
        else:
            raise ValueError("Either 'code' or 'file_path' parameter is required")

    def _create_readme_template(self, name: str, description: str, features: List[str],
                               installation: str, usage: str) -> str:
        """Create README template."""
        features_section = "\n".join([f"- {feature}" for feature in features]) if features else "- Feature 1\n- Feature 2"

        return dedent(f"""
            # {name}

            {description}

            ## Features

            {features_section}

            ## Installation

            ```bash
            {installation}
            ```

            ## Usage

            ```python
            {usage or "# Example usage here"}
            ```

            ## Contributing

            Contributions are welcome! Please feel free to submit a Pull Request.

            ## License

            This project is licensed under the MIT License - see the LICENSE file for details.
        """).strip()

    def _create_changelog_template(self, versions: List[Dict], format_type: str) -> str:
        """Create changelog template."""
        if format_type == "markdown":
            lines = ["# Changelog", ""]
            for version in versions:
                lines.append(f"## [{version.get('version', '1.0.0')}] - {version.get('date', 'TBD')}")
                lines.append("")

                if version.get('added'):
                    lines.append("### Added")
                    for item in version['added']:
                        lines.append(f"- {item}")
                    lines.append("")

                if version.get('changed'):
                    lines.append("### Changed")
                    for item in version['changed']:
                        lines.append(f"- {item}")
                    lines.append("")

                if version.get('fixed'):
                    lines.append("### Fixed")
                    for item in version['fixed']:
                        lines.append(f"- {item}")
                    lines.append("")

            return "\n".join(lines)
        else:
            return "Changelog format not supported"


class DocstringGenerator(ast.NodeVisitor):
    """AST visitor for generating docstrings."""

    def __init__(self, style: str = "google", include_examples: bool = False):
        self.style = style
        self.include_examples = include_examples
        self.docstrings = []
        self.total_items = 0
        self.documented_items = 0

    def visit_FunctionDef(self, node):
        self.total_items += 1

        # Check if already has docstring
        has_docstring = (node.body and
                        isinstance(node.body[0], ast.Expr) and
                        isinstance(node.body[0].value, ast.Constant) and
                        isinstance(node.body[0].value.value, str))

        if has_docstring:
            self.documented_items += 1
        else:
            # Generate docstring
            docstring = self._generate_function_docstring(node)
            self.docstrings.append({
                "type": "function",
                "name": node.name,
                "line": node.lineno,
                "docstring": docstring
            })

        self.generic_visit(node)

    def visit_ClassDef(self, node):
        self.total_items += 1

        # Check if already has docstring
        has_docstring = (node.body and
                        isinstance(node.body[0], ast.Expr) and
                        isinstance(node.body[0].value, ast.Constant) and
                        isinstance(node.body[0].value.value, str))

        if has_docstring:
            self.documented_items += 1
        else:
            # Generate docstring
            docstring = self._generate_class_docstring(node)
            self.docstrings.append({
                "type": "class",
                "name": node.name,
                "line": node.lineno,
                "docstring": docstring
            })

        self.generic_visit(node)

    def _generate_function_docstring(self, node: ast.FunctionDef) -> str:
        """Generate docstring for a function."""
        description = f"Function {node.name}."

        # Extract parameters
        args = []
        for arg in node.args.args:
            arg_info = {"name": arg.arg}
            if arg.annotation:
                arg_info["type"] = ast.unparse(arg.annotation)
            args.append(arg_info)

        # Extract return type
        return_type = "Any"
        if node.returns:
            return_type = ast.unparse(node.returns)

        # Generate based on style
        if self.style == "google":
            args_section = "\n".join([f"    {arg['name']}: Description of {arg['name']}" for arg in args])
            return f'"""{description}\n\nArgs:\n{args_section}\n\nReturns:\n    {return_type}: Return value description\n"""'
        elif self.style == "numpy":
            params_section = "\n".join([f"{arg['name']} : {arg.get('type', 'type')}\n    Description of {arg['name']}" for arg in args])
            return f'"""{description}\n\nParameters\n----------\n{params_section}\n\nReturns\n-------\n{return_type}\n    Return value description\n"""'
        else:  # sphinx
            param_list = ", ".join([arg['name'] for arg in args])
            return f'"""{description}\n\n:param {param_list}: Parameter descriptions\n:returns: Return value description\n:rtype: {return_type}\n"""'

    def _generate_class_docstring(self, node: ast.ClassDef) -> str:
        """Generate docstring for a class."""
        description = f"Class {node.name}."

        # Extract attributes from __init__ if present
        attributes = []
        for item in node.body:
            if isinstance(item, ast.FunctionDef) and item.name == "__init__":
                for stmt in item.body:
                    if isinstance(stmt, ast.Assign):
                        for target in stmt.targets:
                            if isinstance(target, ast.Attribute) and isinstance(target.value, ast.Name):
                                if target.value.id == "self":
                                    attributes.append(target.attr)

        if self.style == "google":
            attrs_section = "\n".join([f"    {attr}: Description of {attr}" for attr in attributes])
            return f'"""{description}\n\nAttributes:\n{attrs_section}\n"""'
        elif self.style == "numpy":
            attrs_section = "\n".join([f"{attr} : type\n    Description of {attr}" for attr in attributes])
            return f'"""{description}\n\nAttributes\n----------\n{attrs_section}\n"""'
        else:  # sphinx
            attrs_list = ", ".join(attributes)
            return f'"""{description}\n\n:ivar {attrs_list}: Attribute descriptions\n"""'

    def get_generated_docstrings(self) -> List[Dict[str, Any]]:
        """Get list of generated docstrings."""
        return self.docstrings

    def calculate_coverage(self) -> float:
        """Calculate documentation coverage percentage."""
        if self.total_items == 0:
            return 100.0
        return (self.documented_items / self.total_items) * 100

    def apply_docstrings(self, code: str) -> str:
        """Apply generated docstrings to code (simplified implementation)."""
        lines = code.split('\n')

        # This is a simplified implementation
        # In practice, would need more sophisticated AST transformation
        for docstring_info in self.docstrings:
            line_num = docstring_info["line"] - 1
            if line_num < len(lines):
                # Find the function/class definition line
                for i in range(line_num, min(line_num + 5, len(lines))):
                    if lines[i].strip().endswith(':'):
                        # Insert docstring after the definition
                        indent = len(lines[i]) - len(lines[i].lstrip())
                        docstring_lines = docstring_info["docstring"].split('\n')
                        indented_docstring = '\n'.join([' ' * (indent + 4) + line for line in docstring_lines])
                        lines.insert(i + 1, indented_docstring)
                        break

        return '\n'.join(lines)


class APIDocGenerator(ast.NodeVisitor):
    """AST visitor for generating API documentation."""

    def __init__(self, format_type: str = "markdown", include_private: bool = False):
        self.format_type = format_type
        self.include_private = include_private
        self.sections = []
        self.current_class = None

    def visit_ClassDef(self, node):
        if not self.include_private and node.name.startswith('_'):
            return

        self.current_class = node.name
        class_doc = self._extract_docstring(node)

        section = {
            "type": "class",
            "name": node.name,
            "docstring": class_doc,
            "methods": [],
            "line": node.lineno
        }

        # Visit methods
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                if not self.include_private and item.name.startswith('_') and item.name != '__init__':
                    continue

                method_doc = self._extract_docstring(item)
                section["methods"].append({
                    "name": item.name,
                    "docstring": method_doc,
                    "parameters": [arg.arg for arg in item.args.args],
                    "line": item.lineno
                })

        self.sections.append(section)
        self.current_class = None

    def visit_FunctionDef(self, node):
        if self.current_class is None:  # Top-level function
            if not self.include_private and node.name.startswith('_'):
                return

            func_doc = self._extract_docstring(node)
            section = {
                "type": "function",
                "name": node.name,
                "docstring": func_doc,
                "parameters": [arg.arg for arg in node.args.args],
                "line": node.lineno
            }
            self.sections.append(section)

    def _extract_docstring(self, node) -> str:
        """Extract docstring from AST node."""
        if (node.body and
            isinstance(node.body[0], ast.Expr) and
            isinstance(node.body[0].value, ast.Constant) and
            isinstance(node.body[0].value.value, str)):
            return node.body[0].value.value
        return "No documentation available."

    def get_sections(self) -> List[Dict[str, Any]]:
        """Get documentation sections."""
        return self.sections

    def generate_documentation(self) -> str:
        """Generate formatted documentation."""
        if self.format_type == "markdown":
            return self._generate_markdown()
        elif self.format_type == "rst":
            return self._generate_rst()
        else:
            return self._generate_html()

    def _generate_markdown(self) -> str:
        """Generate Markdown documentation."""
        lines = ["# API Documentation", ""]

        for section in self.sections:
            if section["type"] == "class":
                lines.append(f"## Class: {section['name']}")
                lines.append("")
                lines.append(section["docstring"])
                lines.append("")

                if section["methods"]:
                    lines.append("### Methods")
                    lines.append("")
                    for method in section["methods"]:
                        lines.append(f"#### {method['name']}({', '.join(method['parameters'])})")
                        lines.append("")
                        lines.append(method["docstring"])
                        lines.append("")

            elif section["type"] == "function":
                lines.append(f"## Function: {section['name']}")
                lines.append("")
                lines.append(f"**Parameters:** {', '.join(section['parameters'])}")
                lines.append("")
                lines.append(section["docstring"])
                lines.append("")

        return "\n".join(lines)

    def _generate_rst(self) -> str:
        """Generate reStructuredText documentation."""
        lines = ["API Documentation", "==================", ""]

        for section in self.sections:
            if section["type"] == "class":
                lines.append(f"Class: {section['name']}")
                lines.append("-" * (len(section['name']) + 7))
                lines.append("")
                lines.append(section["docstring"])
                lines.append("")

        return "\n".join(lines)

    def _generate_html(self) -> str:
        """Generate HTML documentation."""
        html = ["<html><head><title>API Documentation</title></head><body>"]
        html.append("<h1>API Documentation</h1>")

        for section in self.sections:
            if section["type"] == "class":
                html.append(f"<h2>Class: {section['name']}</h2>")
                html.append(f"<p>{section['docstring']}</p>")

        html.append("</body></html>")
        return "\n".join(html)

    def generate_toc(self) -> str:
        """Generate table of contents."""
        lines = ["## Table of Contents", ""]

        for section in self.sections:
            if section["type"] == "class":
                lines.append(f"- [Class: {section['name']}](#{section['name'].lower()})")
                for method in section["methods"]:
                    lines.append(f"  - [{method['name']}](#{method['name'].lower()})")
            elif section["type"] == "function":
                lines.append(f"- [Function: {section['name']}](#{section['name'].lower()})")

        return "\n".join(lines)


class CommentGenerator(ast.NodeVisitor):
    """AST visitor for generating code comments."""

    def __init__(self, comment_style: str = "inline"):
        self.comment_style = comment_style
        self.comments = []
        self.comment_count = 0

    def visit_For(self, node):
        # Add comment for complex loops
        if self._is_complex_loop(node):
            self.comments.append({
                "line": node.lineno,
                "comment": "# Complex loop - consider optimization",
                "type": "performance"
            })
            self.comment_count += 1

        self.generic_visit(node)

    def visit_If(self, node):
        # Add comment for complex conditions
        if self._is_complex_condition(node):
            self.comments.append({
                "line": node.lineno,
                "comment": "# Complex condition - consider extracting to variable",
                "type": "readability"
            })
            self.comment_count += 1

        self.generic_visit(node)

    def visit_Try(self, node):
        # Add comment for exception handling
        self.comments.append({
            "line": node.lineno,
            "comment": "# Exception handling block",
            "type": "structure"
        })
        self.comment_count += 1

        self.generic_visit(node)

    def _is_complex_loop(self, node) -> bool:
        """Check if loop is complex enough to warrant a comment."""
        # Count nested structures
        nested_count = 0
        for child in ast.walk(node):
            if isinstance(child, (ast.For, ast.While, ast.If)) and child != node:
                nested_count += 1

        return nested_count > 2

    def _is_complex_condition(self, node) -> bool:
        """Check if condition is complex enough to warrant a comment."""
        # Count boolean operators
        bool_ops = 0
        for child in ast.walk(node.test):
            if isinstance(child, ast.BoolOp):
                bool_ops += 1

        return bool_ops > 1

    def get_comment_count(self) -> int:
        """Get total number of comments generated."""
        return self.comment_count

    def apply_comments(self, code: str) -> str:
        """Apply comments to code."""
        lines = code.split('\n')

        # Sort comments by line number in reverse order
        sorted_comments = sorted(self.comments, key=lambda x: x["line"], reverse=True)

        for comment_info in sorted_comments:
            line_num = comment_info["line"] - 1
            if 0 <= line_num < len(lines):
                # Add comment before the line
                indent = len(lines[line_num]) - len(lines[line_num].lstrip())
                comment_line = ' ' * indent + comment_info["comment"]
                lines.insert(line_num, comment_line)

        return '\n'.join(lines)


class TypeDocumenter(ast.NodeVisitor):
    """AST visitor for documenting type hints."""

    def __init__(self):
        self.type_info = []
        self.functions_with_types = 0
        self.total_functions = 0

    def visit_FunctionDef(self, node):
        self.total_functions += 1

        # Check for type hints
        has_type_hints = False
        param_types = []

        for arg in node.args.args:
            if arg.annotation:
                has_type_hints = True
                param_types.append({
                    "name": arg.arg,
                    "type": ast.unparse(arg.annotation)
                })
            else:
                param_types.append({
                    "name": arg.arg,
                    "type": "Any"
                })

        return_type = "Any"
        if node.returns:
            has_type_hints = True
            return_type = ast.unparse(node.returns)

        if has_type_hints:
            self.functions_with_types += 1

        self.type_info.append({
            "name": node.name,
            "line": node.lineno,
            "parameters": param_types,
            "return_type": return_type,
            "has_type_hints": has_type_hints
        })

        self.generic_visit(node)

    def generate_type_docs(self) -> str:
        """Generate type documentation."""
        lines = ["# Type Documentation", ""]

        for func_info in self.type_info:
            lines.append(f"## {func_info['name']}")
            lines.append("")
            lines.append("**Parameters:**")
            for param in func_info['parameters']:
                lines.append(f"- `{param['name']}`: {param['type']}")
            lines.append("")
            lines.append(f"**Returns:** {func_info['return_type']}")
            lines.append("")

        return "\n".join(lines)

    def calculate_type_coverage(self) -> float:
        """Calculate type hint coverage percentage."""
        if self.total_functions == 0:
            return 100.0
        return (self.functions_with_types / self.total_functions) * 100