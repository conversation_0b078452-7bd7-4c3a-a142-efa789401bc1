"""
Core orchestrator for the agent framework.

The AgentOrchestrator is the central coordination system that manages
agent lifecycle, task execution, and coordination between components.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional
from uuid import UUID

try:
    import psutil
except ImportError:
    psutil = None

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient

from .config import FrameworkConfig
from .types import (
    Task, TaskResult, TaskStatus, TaskPriority,
    PluginInterface, PluginRequest, PluginResponse,
    ContextQuery, Context, AgentEvent, ResourceMetrics
)
from .agent_manager import AgentManager
from .agent_registry import AgentRegistry
from .multi_agent_types import AgentRoleConfig, MultiAgentEvent
from ..plugins.manager import PluginManager
from ..context.manager import ContextManager
from ..execution.executor import TaskExecutor
from ..communication.broker import MessageBroker
from ..mcp.connection_manager import MCPConnectionManager
from ..coordination.coordinator import AgentCoordinator
from ..monitoring.multi_agent_logger import MultiAgentLogger
from ..agents import (
    CodeAnalysisAgent, TestingAgent, DocumentationAgent,
    RefactoringAgent, ErrorDetectionAgent, OptimizationAgent
)


class AgentOrchestrator:
    """
    Central orchestration system for the agent framework.

    Manages agent lifecycle, task execution, plugin coordination,
    and communication between all framework components.
    """

    def __init__(self, config: FrameworkConfig):
        """Initialize the orchestrator with configuration."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Core components
        self._model_client: Optional[OpenAIChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._plugin_manager: Optional[PluginManager] = None
        self._context_manager: Optional[ContextManager] = None
        self._task_executor: Optional[TaskExecutor] = None
        self._message_broker: Optional[MessageBroker] = None

        # Multi-agent components
        self._mcp_manager: Optional[MCPConnectionManager] = None
        self._agent_registry: Optional[AgentRegistry] = None
        self._agent_manager: Optional[AgentManager] = None
        self._agent_coordinator: Optional[AgentCoordinator] = None
        self._multi_agent_logger: Optional[MultiAgentLogger] = None

        # Specialized agents
        self._specialized_agents: Dict[str, Any] = {}

        # State management
        self._is_initialized = False
        self._is_running = False
        self._shutdown_event = asyncio.Event()

        # Metrics and monitoring
        self._start_time: Optional[float] = None
        self._task_count = 0
        self._error_count = 0

    async def initialize(self) -> None:
        """Initialize all framework components."""
        if self._is_initialized:
            self.logger.warning("Orchestrator already initialized")
            return

        try:
            self.logger.info("Initializing agent framework...")
            self._start_time = time.time()

            # Validate configuration
            config_errors = self.config.validate_config()
            if config_errors:
                raise ValueError(f"Configuration errors: {', '.join(config_errors)}")

            # Initialize model client
            await self._initialize_model_client()

            # Initialize core components
            await self._initialize_components()

            # Initialize multi-agent system if enabled
            if self.config.multi_agent.enabled:
                await self._initialize_multi_agent_system()

            # Initialize agent
            await self._initialize_agent()

            # Load plugins
            await self._load_plugins()

            self._is_initialized = True
            self._is_running = True
            self.logger.info("Agent framework initialized successfully")

            # Emit initialization event
            await self._emit_event("framework.initialized", {
                "config": self.config.model_dump(),
                "components": self._get_component_status()
            })

        except Exception as e:
            self.logger.error(f"Failed to initialize framework: {e}")
            await self.shutdown()
            raise

    async def _initialize_model_client(self) -> None:
        """Initialize the AI model client."""
        self.logger.info("Initializing model client...")

        self._model_client = OpenAIChatCompletionClient(
            model=self.config.model.model,
            api_key=self.config.model.api_key,
            base_url=self.config.model.base_url,
            model_info=self.config.model.model_info,
        )

        self.logger.info(f"Model client initialized: {self.config.model.model}")

    async def _initialize_components(self) -> None:
        """Initialize all framework components."""
        self.logger.info("Initializing framework components...")

        # Initialize message broker first (other components depend on it)
        from ..communication.broker import MessageBroker
        self._message_broker = MessageBroker(self.config)
        await self._message_broker.initialize()

        # Initialize context manager
        from ..context.manager import ContextManager
        self._context_manager = ContextManager(self.config, self._message_broker)
        await self._context_manager.initialize()

        # Initialize task executor
        from ..execution.executor import TaskExecutor
        self._task_executor = TaskExecutor(self.config, self._message_broker)
        await self._task_executor.initialize()

        # Initialize plugin manager
        from ..plugins.manager import PluginManager
        self._plugin_manager = PluginManager(self.config, self._message_broker)
        await self._plugin_manager.initialize()

        self.logger.info("Framework components initialized")

    async def _initialize_agent(self) -> None:
        """Initialize the main assistant agent."""
        if not self._model_client:
            raise RuntimeError("Model client not initialized")

        self.logger.info("Initializing assistant agent...")

        # Get available tools from plugins
        tools = []
        if self._plugin_manager:
            tools = await self._plugin_manager.get_all_tools()

        # Create assistant agent
        self._agent = AssistantAgent(
            name="programming_assistant",
            model_client=self._model_client,
            tools=tools,
            system_message=self._get_system_message(),
            reflect_on_tool_use=True,
            model_client_stream=True,
        )

        self.logger.info("Assistant agent initialized")

    async def _initialize_multi_agent_system(self) -> None:
        """Initialize the multi-agent system."""
        self.logger.info("Initializing multi-agent system...")

        # Initialize multi-agent logger
        self._multi_agent_logger = MultiAgentLogger(
            log_dir=self.config.logging.file_path or "logs",
            log_level=self.config.logging.level,
            max_file_size=self.config.logging.max_file_size,
            backup_count=self.config.logging.backup_count
        )

        # Initialize MCP connection manager
        self._mcp_manager = MCPConnectionManager(self.config.mcp)
        await self._mcp_manager.start()

        # Initialize agent registry
        self._agent_registry = AgentRegistry()

        # Initialize agent manager
        self._agent_manager = AgentManager(
            self._agent_registry,
            self._message_broker,
            self.config.multi_agent.coordination_strategy
        )
        await self._agent_manager.start()

        # Initialize agent coordinator
        self._agent_coordinator = AgentCoordinator(
            self._agent_manager,
            self._agent_registry,
            self._message_broker
        )

        # Initialize specialized agents
        await self._initialize_specialized_agents()

        self.logger.info("Multi-agent system initialized")

    async def _initialize_specialized_agents(self) -> None:
        """Initialize specialized agents based on configuration."""
        for role_name, role_config in self.config.multi_agent.agent_roles.items():
            try:
                agent = await self._create_specialized_agent(role_config)
                if agent:
                    await agent.initialize({})
                    agent_id = await self._agent_registry.register_agent(agent)
                    self._specialized_agents[role_name] = agent

                    self.logger.info(f"Initialized specialized agent: {role_name} ({agent_id})")

                    if self._multi_agent_logger:
                        self._multi_agent_logger.log_agent_event(
                            agent_id,
                            "initialized",
                            {"role": role_name},
                            agent.agent_info
                        )

            except Exception as e:
                self.logger.error(f"Failed to initialize agent {role_name}: {e}")

    async def _create_specialized_agent(self, role_config: AgentRoleConfig):
        """Create a specialized agent based on role configuration."""
        # Map role names to agent classes
        agent_classes = {
            "code_analysis": CodeAnalysisAgent,
            "testing": TestingAgent,
            "documentation": DocumentationAgent,
            "refactoring": RefactoringAgent,
            "error_detection": ErrorDetectionAgent,
            "optimization": OptimizationAgent
        }

        # Determine agent class based on role name or capabilities
        agent_class = None
        role_lower = role_config.name.lower()

        for role_key, cls in agent_classes.items():
            if role_key in role_lower:
                agent_class = cls
                break

        if not agent_class:
            # Default to code analysis agent
            agent_class = CodeAnalysisAgent

        # Create agent with role configuration and MCP manager
        return agent_class(role_config, mcp_manager=self._mcp_manager)

    def _get_system_message(self) -> str:
        """Get the system message for the assistant agent."""
        return """You are a comprehensive programming assistant agent with access to various tools and plugins.

Your capabilities include:
- Code analysis and understanding
- Refactoring and optimization suggestions
- Test generation and validation
- Documentation generation
- Integration with development tools

Always provide helpful, accurate, and actionable assistance to developers.
Use the available tools and plugins to enhance your responses when appropriate.
"""

    async def _load_plugins(self) -> None:
        """Load and initialize plugins."""
        if not self._plugin_manager:
            self.logger.warning("Plugin manager not initialized, skipping plugin loading")
            return

        if self.config.plugins.auto_load_plugins:
            self.logger.info("Auto-loading plugins...")
            await self._plugin_manager.load_all_plugins()

        self.logger.info(f"Loaded {len(self._plugin_manager.get_loaded_plugins())} plugins")

    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a task using the framework."""
        if not self._is_initialized:
            raise RuntimeError("Framework not initialized")

        if not self._task_executor:
            raise RuntimeError("Task executor not available")

        self.logger.info(f"Executing task: {task.name} ({task.id})")
        self._task_count += 1

        try:
            # Emit task started event
            await self._emit_event("task.started", {
                "task_id": str(task.id),
                "task_name": task.name,
                "task_type": task.task_type
            })

            # Execute the task
            result = await self._task_executor.execute_task(task)

            # Emit task completed event
            await self._emit_event("task.completed", {
                "task_id": str(task.id),
                "status": result.status.value,
                "execution_time": result.execution_time
            })

            return result

        except Exception as e:
            self._error_count += 1
            self.logger.error(f"Task execution failed: {e}")

            # Emit task failed event
            await self._emit_event("task.failed", {
                "task_id": str(task.id),
                "error": str(e)
            })

            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )

    async def execute_multi_agent_task(self, task: Task) -> TaskResult:
        """
        Execute a task using the multi-agent system.

        Args:
            task: The task to execute

        Returns:
            Task execution result
        """
        if not self.config.multi_agent.enabled or not self._agent_coordinator:
            # Fallback to single-agent execution
            return await self.execute_task(task)

        self.logger.info(f"Executing multi-agent task: {task.name} ({task.id})")

        try:
            # Use agent coordinator for complex task execution
            result = await self._agent_coordinator.coordinate_task(task)

            # Log multi-agent execution
            if self._multi_agent_logger:
                self._multi_agent_logger.log_coordination_event(
                    task.id,
                    "task_completed",
                    {
                        "task_name": task.name,
                        "result_status": result.status.value,
                        "execution_time": result.execution_time
                    }
                )

            return result

        except Exception as e:
            self.logger.error(f"Multi-agent task execution failed: {e}")

            if self._multi_agent_logger:
                self._multi_agent_logger.log_error(
                    e,
                    {"task_id": str(task.id), "task_name": task.name},
                    task_id=task.id
                )

            raise

    async def get_multi_agent_status(self) -> Dict[str, Any]:
        """
        Get status of the multi-agent system.

        Returns:
            Multi-agent system status
        """
        if not self.config.multi_agent.enabled:
            return {"enabled": False}

        status = {"enabled": True}

        if self._agent_registry:
            status["registry_stats"] = await self._agent_registry.get_registry_stats()

        if self._agent_manager:
            status["coordination_status"] = await self._agent_manager.get_coordination_status()

        if self._mcp_manager:
            status["mcp_stats"] = await self._mcp_manager.get_connection_stats()

        status["specialized_agents"] = list(self._specialized_agents.keys())

        return status

    async def load_plugin(self, plugin_name: str) -> PluginInterface:
        """Load a specific plugin by name."""
        if not self._plugin_manager:
            raise RuntimeError("Plugin manager not available")

        return await self._plugin_manager.load_plugin(plugin_name)

    async def get_context(self, query: ContextQuery) -> Context:
        """Retrieve context information."""
        if not self._context_manager:
            raise RuntimeError("Context manager not available")

        return await self._context_manager.get_context(query)

    async def run_agent_task(self, task_description: str) -> str:
        """Run a task using the assistant agent directly."""
        if not self._agent:
            raise RuntimeError("Agent not initialized")

        self.logger.info(f"Running agent task: {task_description}")

        try:
            result = await self._agent.run(task=task_description)

            # Extract the final response
            if result.messages:
                return result.messages[-1].content
            else:
                return "No response generated"

        except Exception as e:
            self.logger.error(f"Agent task failed: {e}")
            raise

    async def get_metrics(self) -> ResourceMetrics:
        """Get current system metrics."""
        import psutil

        # Get system metrics
        cpu_usage = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Get framework-specific metrics
        active_tasks = 0
        plugin_count = 0

        if self._task_executor:
            active_tasks = await self._task_executor.get_active_task_count()

        if self._plugin_manager:
            plugins = self._plugin_manager.get_loaded_plugins()
            plugin_count = len(plugins)

        return ResourceMetrics(
            cpu_usage=cpu_usage,
            memory_usage=memory.percent,
            disk_usage=disk.percent,
            network_usage=0.0,  # Would need network monitoring
            active_tasks=active_tasks,
            plugin_count=plugin_count
        )

    async def shutdown(self) -> None:
        """Shutdown the framework and cleanup resources."""
        if not self._is_initialized:
            return

        self.logger.info("Shutting down agent framework...")
        self._shutdown_event.set()

        try:
            # Shutdown components in reverse order
            if self._plugin_manager:
                await self._plugin_manager.shutdown()

            if self._task_executor:
                await self._task_executor.shutdown()

            if self._context_manager:
                await self._context_manager.shutdown()

            if self._message_broker:
                await self._message_broker.shutdown()

            if self._model_client:
                await self._model_client.close()

            # Emit shutdown event
            await self._emit_event("framework.shutdown", {
                "uptime": time.time() - (self._start_time or 0),
                "tasks_processed": self._task_count,
                "errors": self._error_count
            })

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

        self._is_initialized = False
        self._is_running = False
        self.logger.info("Agent framework shutdown complete")

    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Emit an event through the message broker."""
        if self._message_broker:
            event = AgentEvent(
                event_type=event_type,
                source="orchestrator",
                data=data
            )
            await self._message_broker.publish_event(event)

    def _get_component_status(self) -> Dict[str, bool]:
        """Get the status of all components."""
        return {
            "model_client": self._model_client is not None,
            "agent": self._agent is not None,
            "plugin_manager": self._plugin_manager is not None,
            "context_manager": self._context_manager is not None,
            "task_executor": self._task_executor is not None,
            "message_broker": self._message_broker is not None,
        }

    @property
    def is_initialized(self) -> bool:
        """Check if the framework is initialized."""
        return self._is_initialized

    @property
    def is_running(self) -> bool:
        """Check if the framework is running."""
        return self._is_running