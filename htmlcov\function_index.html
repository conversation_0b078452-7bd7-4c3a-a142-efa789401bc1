<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">82%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-31 18:07 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_89f505f698d59396___init___py.html">agent_framework\__init__.py</a></td>
                <td class="name left"><a href="z_89f505f698d59396___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69___init___py.html">agent_framework\communication\__init__.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t22">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t22"><data value='init__'>MessageBroker.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t33">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t33"><data value='initialize'>MessageBroker.initialize</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t43">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t43"><data value='publish_event'>MessageBroker.publish_event</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t50">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t50"><data value='subscribe'>MessageBroker.subscribe</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t55">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t55"><data value='unsubscribe'>MessageBroker.unsubscribe</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t64">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t64"><data value='process_events'>MessageBroker._process_events</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t82">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t82"><data value='notify_subscribers'>MessageBroker._notify_subscribers</data></a></td>
                <td>13</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="3 13">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t106">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t106"><data value='shutdown'>MessageBroker.shutdown</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765___init___py.html">agent_framework\context\__init__.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t21">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t21"><data value='init__'>ContextManager.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t33">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t33"><data value='initialize'>ContextManager.initialize</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t46">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t46"><data value='initialize_storage'>ContextManager._initialize_storage</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t52">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t52"><data value='get_context'>ContextManager.get_context</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t87">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t87"><data value='get_codebase_context'>ContextManager._get_codebase_context</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t99">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t99"><data value='get_session_context'>ContextManager._get_session_context</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t115">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t115"><data value='get_global_context'>ContextManager._get_global_context</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t128">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t128"><data value='store_context'>ContextManager.store_context</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t140">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t140"><data value='clear_context'>ContextManager.clear_context</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t149">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t149"><data value='get_context_stats'>ContextManager.get_context_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t159">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t159"><data value='shutdown'>ContextManager.shutdown</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c___init___py.html">agent_framework\core\__init__.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t122">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t122"><data value='from_file'>FrameworkConfig.from_file</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t142">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t142"><data value='from_env'>FrameworkConfig.from_env</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t166">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t166"><data value='to_file'>FrameworkConfig.to_file</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t184">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t184"><data value='validate_config'>FrameworkConfig.validate_config</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>80</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="80 80">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t42">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t42"><data value='init__'>AgentOrchestrator.__init__</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t65">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t65"><data value='initialize'>AgentOrchestrator.initialize</data></a></td>
                <td>21</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="18 21">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t107">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t107"><data value='initialize_model_client'>AgentOrchestrator._initialize_model_client</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t120">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t120"><data value='initialize_components'>AgentOrchestrator._initialize_components</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t146">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t146"><data value='initialize_agent'>AgentOrchestrator._initialize_agent</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t170">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t170"><data value='get_system_message'>AgentOrchestrator._get_system_message</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t185">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t185"><data value='load_plugins'>AgentOrchestrator._load_plugins</data></a></td>
                <td>7</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="3 7">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t197">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t197"><data value='execute_task'>AgentOrchestrator.execute_task</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t244">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t244"><data value='load_plugin'>AgentOrchestrator.load_plugin</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t251">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t251"><data value='get_context'>AgentOrchestrator.get_context</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t258">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t258"><data value='run_agent_task'>AgentOrchestrator.run_agent_task</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t278">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t278"><data value='get_metrics'>AgentOrchestrator.get_metrics</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t307">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t307"><data value='shutdown'>AgentOrchestrator.shutdown</data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t346">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t346"><data value='emit_event'>AgentOrchestrator._emit_event</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t356">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t356"><data value='get_component_status'>AgentOrchestrator._get_component_status</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t368">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t368"><data value='is_initialized'>AgentOrchestrator.is_initialized</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t373">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t373"><data value='is_running'>AgentOrchestrator.is_running</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="35 37">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t52">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t52"><data value='lt__'>Task.__lt__</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t59">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t59"><data value='eq__'>Task.__eq__</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t65">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t65"><data value='hash__'>Task.__hash__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t148">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t148"><data value='initialize'>PluginInterface.initialize</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t153">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t153"><data value='execute'>PluginInterface.execute</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t158">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t158"><data value='get_capabilities'>PluginInterface.get_capabilities</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t163">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t163"><data value='cleanup'>PluginInterface.cleanup</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t169">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t169"><data value='name'>PluginInterface.name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t175">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t175"><data value='version'>PluginInterface.version</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>104</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="104 104">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270___init___py.html">agent_framework\execution\__init__.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t23">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t23"><data value='init__'>TaskExecutor.__init__</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t41">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t41"><data value='initialize'>TaskExecutor.initialize</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t59">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t59"><data value='execute_task'>TaskExecutor.execute_task</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t76">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t76"><data value='get_task_priority'>TaskExecutor._get_task_priority</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t86">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t86"><data value='wait_for_task_completion'>TaskExecutor._wait_for_task_completion</data></a></td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 12">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t116">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t116"><data value='worker'>TaskExecutor._worker</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t139">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t139"><data value='execute_single_task'>TaskExecutor._execute_single_task</data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t200">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t200"><data value='run_task_logic'>TaskExecutor._run_task_logic</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t217">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t217"><data value='run_code_analysis_task'>TaskExecutor._run_code_analysis_task</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t223">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t223"><data value='run_refactoring_task'>TaskExecutor._run_refactoring_task</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t229">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t229"><data value='run_test_generation_task'>TaskExecutor._run_test_generation_task</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t235">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t235"><data value='cleanup_old_results'>TaskExecutor._cleanup_old_results</data></a></td>
                <td>4</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="1 4">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t248">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t248"><data value='get_active_task_count'>TaskExecutor.get_active_task_count</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t252">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t252"><data value='get_queue_size'>TaskExecutor.get_queue_size</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t256">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t256"><data value='cancel_task'>TaskExecutor.cancel_task</data></a></td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="2 4">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t263">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t263"><data value='shutdown'>TaskExecutor.shutdown</data></a></td>
                <td>14</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="13 14">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039___init___py.html">agent_framework\plugins\__init__.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t25">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t25"><data value='init__'>PluginLoader.__init__</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t33">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t33"><data value='discover_plugins'>PluginLoader.discover_plugins</data></a></td>
                <td>18</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="13 18">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t61">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t61"><data value='extract_plugin_metadata'>PluginLoader._extract_plugin_metadata</data></a></td>
                <td>23</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="18 23">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t110">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t110"><data value='load_plugin'>PluginLoader.load_plugin</data></a></td>
                <td>28</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="17 28">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t163">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t163"><data value='load_module'>PluginLoader._load_module</data></a></td>
                <td>17</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="12 17">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t197">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t197"><data value='unload_plugin'>PluginLoader.unload_plugin</data></a></td>
                <td>11</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="7 11">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t219">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t219"><data value='reload_plugin'>PluginLoader.reload_plugin</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t224">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t224"><data value='get_loaded_plugins'>PluginLoader.get_loaded_plugins</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t228">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t228"><data value='is_plugin_loaded'>PluginLoader.is_plugin_loaded</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t232">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t232"><data value='cleanup_all'>PluginLoader.cleanup_all</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t23">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t23"><data value='init__'>PluginManager.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t39">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t39"><data value='initialize'>PluginManager.initialize</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t57">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t57"><data value='discover_plugins'>PluginManager.discover_plugins</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t75">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t75"><data value='load_plugin'>PluginManager.load_plugin</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t79">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t79"><data value='load_all_plugins'>PluginManager.load_all_plugins</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t100">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t100"><data value='unload_plugin'>PluginManager.unload_plugin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t104">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t104"><data value='reload_plugin'>PluginManager.reload_plugin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t108">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t108"><data value='execute_plugin_request'>PluginManager.execute_plugin_request</data></a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="7 14">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t141">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t141"><data value='get_plugin_capabilities'>PluginManager.get_plugin_capabilities</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t152">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t152"><data value='get_all_capabilities'>PluginManager.get_all_capabilities</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t165">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t165"><data value='find_plugins_by_capability'>PluginManager.find_plugins_by_capability</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t181">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t181"><data value='get_all_tools'>PluginManager.get_all_tools</data></a></td>
                <td>12</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="4 12">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t199">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t199"><data value='get_loaded_plugins'>PluginManager.get_loaded_plugins</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t203">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t203"><data value='get_plugin_registry'>PluginManager.get_plugin_registry</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t207">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t207"><data value='get_plugin_metadata'>PluginManager.get_plugin_metadata</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t211">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t211"><data value='enable_plugin'>PluginManager.enable_plugin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t215">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t215"><data value='disable_plugin'>PluginManager.disable_plugin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t219">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t219"><data value='get_plugin_status'>PluginManager.get_plugin_status</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t241">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t241"><data value='shutdown'>PluginManager.shutdown</data></a></td>
                <td>10</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="7 10">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t262">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t262"><data value='is_initialized'>PluginManager.is_initialized</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t40">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t40"><data value='init__'>PluginRegistry.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t47">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t47"><data value='register_plugin'>PluginRegistry.register_plugin</data></a></td>
                <td>16</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="14 16">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t81">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t81"><data value='unregister_plugin'>PluginRegistry.unregister_plugin</data></a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="7 14">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t107">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t107"><data value='get_plugin'>PluginRegistry.get_plugin</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t111">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t111"><data value='get_all_plugins'>PluginRegistry.get_all_plugins</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t115">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t115"><data value='get_enabled_plugins'>PluginRegistry.get_enabled_plugins</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t123">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t123"><data value='get_plugins_by_capability'>PluginRegistry.get_plugins_by_capability</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t132">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t132"><data value='get_plugin_dependencies'>PluginRegistry.get_plugin_dependencies</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t136">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t136"><data value='get_dependency_order'>PluginRegistry.get_dependency_order</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t141">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t141"><data value='visit'>PluginRegistry.get_dependency_order.visit</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t159">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t159"><data value='validate_dependencies'>PluginRegistry.validate_dependencies</data></a></td>
                <td>8</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="6 8">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t172">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t172"><data value='enable_plugin'>PluginRegistry.enable_plugin</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t180">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t180"><data value='disable_plugin'>PluginRegistry.disable_plugin</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t188">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t188"><data value='update_load_stats'>PluginRegistry.update_load_stats</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t198">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t198"><data value='get_registry_stats'>PluginRegistry.get_registry_stats</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t211">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t211"><data value='clear'>PluginRegistry.clear</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1111</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="916 1111">82%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-31 18:07 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
