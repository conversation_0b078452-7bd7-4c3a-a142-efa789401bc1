"""
Base command classes and command registry for the CLI.

Provides the foundation for implementing CLI commands with consistent
interface and registration mechanism.
"""

import argparse
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator


class BaseCommand(ABC):
    """Base class for CLI commands."""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Get command name."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Get command description."""
        pass
    
    @abstractmethod
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments to parser."""
        pass
    
    @abstractmethod
    async def execute(self, args: argparse.Namespace, 
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the command."""
        pass
    
    def get_help_text(self) -> str:
        """Get detailed help text for the command."""
        return self.description


class CommandRegistry:
    """Registry for CLI commands."""
    
    def __init__(self):
        """Initialize command registry."""
        self._commands: Dict[str, BaseCommand] = {}
        self._register_default_commands()
    
    def _register_default_commands(self) -> None:
        """Register default commands."""
        from .analyze import AnalyzeCommand
        from .generate import GenerateCommand
        from .optimize import OptimizeCommand
        from .debug import DebugCommand
        from .document import DocumentCommand
        from .interactive import InteractiveCommand
        
        commands = [
            AnalyzeCommand(),
            GenerateCommand(),
            OptimizeCommand(),
            DebugCommand(),
            DocumentCommand(),
            InteractiveCommand()
        ]
        
        for command in commands:
            self.register_command(command)
    
    def register_command(self, command: BaseCommand) -> None:
        """Register a command."""
        self._commands[command.name] = command
    
    def get_command(self, name: str) -> Optional[BaseCommand]:
        """Get command by name."""
        return self._commands.get(name)
    
    def get_all_commands(self) -> Dict[str, BaseCommand]:
        """Get all registered commands."""
        return self._commands.copy()
    
    def register_commands(self, subparsers) -> None:
        """Register all commands with argument parser."""
        for command in self._commands.values():
            # Create subparser for command
            cmd_parser = subparsers.add_parser(
                command.name,
                help=command.description,
                description=command.get_help_text(),
                formatter_class=argparse.RawDescriptionHelpFormatter
            )
            
            # Add command-specific arguments
            command.add_arguments(cmd_parser)


class FileInputMixin:
    """Mixin for commands that accept file input."""
    
    def add_file_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add file input arguments."""
        input_group = parser.add_mutually_exclusive_group(required=True)
        input_group.add_argument(
            '--file', '-f',
            type=str,
            help='Path to input file'
        )
        input_group.add_argument(
            '--code', '-c',
            type=str,
            help='Code string to process'
        )
        input_group.add_argument(
            '--stdin',
            action='store_true',
            help='Read from standard input'
        )
    
    def get_code_input(self, args: argparse.Namespace) -> str:
        """Get code input from arguments."""
        import sys
        from pathlib import Path
        
        if args.file:
            file_path = Path(args.file)
            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {args.file}")
            return file_path.read_text(encoding='utf-8')
        elif args.code:
            return args.code
        elif args.stdin:
            return sys.stdin.read()
        else:
            raise ValueError("No input provided")


class OutputFormatMixin:
    """Mixin for commands that support different output formats."""
    
    def add_output_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add output format arguments."""
        parser.add_argument(
            '--format', '-F',
            choices=['text', 'json', 'yaml', 'table'],
            default='text',
            help='Output format (default: text)'
        )
        parser.add_argument(
            '--output', '-o',
            type=str,
            help='Output file path (default: stdout)'
        )
        parser.add_argument(
            '--pretty',
            action='store_true',
            help='Pretty-print output'
        )
    
    def format_output(self, data: Any, format_type: str, pretty: bool = False) -> str:
        """Format output data according to specified format."""
        if format_type == 'json':
            import json
            return json.dumps(data, indent=2 if pretty else None, ensure_ascii=False)
        elif format_type == 'yaml':
            import yaml
            return yaml.dump(data, default_flow_style=not pretty, allow_unicode=True)
        elif format_type == 'table' and isinstance(data, list):
            from ..utils import TableFormatter
            if data and isinstance(data[0], dict):
                headers = list(data[0].keys())
                rows = [[str(item.get(header, '')) for header in headers] for item in data]
                return TableFormatter.format_table(headers, rows, 'grid')
            else:
                return str(data)
        else:
            return str(data)
    
    def write_output(self, content: str, output_path: Optional[str] = None) -> None:
        """Write output to file or stdout."""
        if output_path:
            from pathlib import Path
            Path(output_path).write_text(content, encoding='utf-8')
        else:
            print(content)


class PluginCommandMixin:
    """Mixin for commands that interact with plugins."""
    
    def add_plugin_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add plugin-related arguments."""
        parser.add_argument(
            '--plugin',
            type=str,
            help='Specific plugin to use'
        )
        parser.add_argument(
            '--list-plugins',
            action='store_true',
            help='List available plugins'
        )
    
    async def list_plugins(self, orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """List available plugins."""
        if not orchestrator._plugin_manager:
            return {"plugins": [], "message": "Plugin manager not available"}
        
        plugins = orchestrator._plugin_manager.get_loaded_plugins()
        plugin_info = []
        
        for plugin_name, plugin in plugins.items():
            capabilities = await plugin.get_capabilities()
            plugin_info.append({
                "name": plugin_name,
                "version": plugin.version,
                "capabilities": [cap.name for cap in capabilities]
            })
        
        return {
            "plugins": plugin_info,
            "count": len(plugin_info)
        }


class ValidationMixin:
    """Mixin for input validation."""
    
    def validate_file_exists(self, file_path: str) -> bool:
        """Validate that file exists."""
        from pathlib import Path
        return Path(file_path).exists()
    
    def validate_python_code(self, code: str) -> bool:
        """Validate Python code syntax."""
        import ast
        try:
            ast.parse(code)
            return True
        except SyntaxError:
            return False
    
    def validate_required_args(self, args: argparse.Namespace, 
                             required_fields: list) -> None:
        """Validate that required arguments are provided."""
        missing = []
        for field in required_fields:
            if not hasattr(args, field) or getattr(args, field) is None:
                missing.append(field)
        
        if missing:
            raise ValueError(f"Missing required arguments: {', '.join(missing)}")


class AsyncCommandBase(BaseCommand):
    """Base class for async commands with common functionality."""
    
    async def handle_plugin_request(self, orchestrator: AgentOrchestrator,
                                  plugin_name: str, capability: str,
                                  parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Handle plugin request with error handling."""
        try:
            if not orchestrator._plugin_manager:
                return {"success": False, "error": "Plugin manager not available"}
            
            # Load plugin if not already loaded
            plugin = await orchestrator._plugin_manager.load_plugin(plugin_name)
            
            # Create plugin request
            from ...core.types import PluginRequest
            request = PluginRequest(
                capability=capability,
                parameters=parameters
            )
            
            # Execute request
            response = await plugin.execute(request)
            
            return {
                "success": response.success,
                "result": response.result,
                "error": response.error,
                "execution_time": response.execution_time,
                "metadata": response.metadata
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
