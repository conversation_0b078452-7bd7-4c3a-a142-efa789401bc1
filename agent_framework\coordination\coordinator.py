"""
Main coordinator for multi-agent collaboration.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

from ..core.agent_manager import Agent<PERSON>anager
from ..core.agent_registry import AgentRegistry
from ..core.multi_agent_types import (
    AgentCapability, TaskDelegationRequest, TaskDelegationResponse,
    CoordinationStrategy, MultiAgentEvent
)
from ..core.types import Task, TaskResult, TaskStatus
from ..communication.broker import MessageBroker
from .task_delegator import TaskDelegator
from .result_aggregator import ResultAggregator
from .workflow_engine import WorkflowEngine


class AgentCoordinator:
    """
    Main coordinator for multi-agent collaboration.
    
    Orchestrates complex tasks by:
    - Breaking down tasks into subtasks
    - Delegating subtasks to appropriate agents
    - Coordinating agent interactions
    - Aggregating results
    - Managing workflows
    """
    
    def __init__(self, 
                 agent_manager: AgentManager,
                 agent_registry: AgentRegistry,
                 message_broker: MessageBroker):
        """
        Initialize the agent coordinator.
        
        Args:
            agent_manager: Agent manager instance
            agent_registry: Agent registry instance
            message_broker: Message broker for communication
        """
        self.agent_manager = agent_manager
        self.agent_registry = agent_registry
        self.message_broker = message_broker
        self.logger = logging.getLogger(__name__)
        
        # Coordination components
        self.task_delegator = TaskDelegator(agent_manager, agent_registry)
        self.result_aggregator = ResultAggregator()
        self.workflow_engine = WorkflowEngine(agent_manager, agent_registry)
        
        # State management
        self._active_coordinations: Dict[UUID, Dict[str, Any]] = {}
        self._coordination_history: List[Dict[str, Any]] = []
        
    async def coordinate_task(self, 
                            task: Task,
                            coordination_strategy: CoordinationStrategy = CoordinationStrategy.CAPABILITY_BASED) -> TaskResult:
        """
        Coordinate execution of a complex task across multiple agents.
        
        Args:
            task: The task to coordinate
            coordination_strategy: Strategy for coordination
            
        Returns:
            Aggregated task result
        """
        self.logger.info(f"Coordinating task: {task.name}")
        
        coordination_id = task.id
        self._active_coordinations[coordination_id] = {
            "task": task,
            "strategy": coordination_strategy,
            "subtasks": [],
            "results": [],
            "status": "in_progress"
        }
        
        try:
            # Analyze task and determine coordination approach
            coordination_plan = await self._analyze_task_for_coordination(task)
            
            if coordination_plan["requires_coordination"]:
                # Multi-agent coordination required
                result = await self._execute_coordinated_task(task, coordination_plan)
            else:
                # Single agent can handle the task
                result = await self._execute_single_agent_task(task)
            
            # Record coordination history
            self._record_coordination_completion(coordination_id, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Coordination failed for task {task.name}: {e}")
            result = TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )
            self._record_coordination_completion(coordination_id, result)
            return result
        
        finally:
            # Clean up active coordination
            self._active_coordinations.pop(coordination_id, None)
    
    async def coordinate_workflow(self, 
                                workflow_definition: Dict[str, Any]) -> Dict[str, Any]:
        """
        Coordinate execution of a multi-step workflow.
        
        Args:
            workflow_definition: Definition of the workflow
            
        Returns:
            Workflow execution results
        """
        self.logger.info(f"Coordinating workflow: {workflow_definition.get('name', 'unnamed')}")
        
        return await self.workflow_engine.execute_workflow(workflow_definition)
    
    async def get_coordination_status(self, coordination_id: UUID) -> Optional[Dict[str, Any]]:
        """
        Get the status of an active coordination.
        
        Args:
            coordination_id: ID of the coordination
            
        Returns:
            Coordination status information
        """
        return self._active_coordinations.get(coordination_id)
    
    async def get_coordination_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get coordination history.
        
        Args:
            limit: Maximum number of records to return
            
        Returns:
            List of coordination history records
        """
        return self._coordination_history[-limit:]
    
    async def _analyze_task_for_coordination(self, task: Task) -> Dict[str, Any]:
        """
        Analyze a task to determine if coordination is needed.
        
        Args:
            task: Task to analyze
            
        Returns:
            Coordination analysis results
        """
        # Determine required capabilities
        required_capabilities = await self._extract_required_capabilities(task)
        
        # Find agents with required capabilities
        suitable_agents = []
        for capability in required_capabilities:
            agents = await self.agent_registry.find_agents_by_capability(capability)
            suitable_agents.extend(agents)
        
        # Remove duplicates
        suitable_agents = list(set(suitable_agents))
        
        # Determine if coordination is needed
        requires_coordination = (
            len(required_capabilities) > 1 or  # Multiple capabilities needed
            len(suitable_agents) > 1 or        # Multiple agents available
            task.task_type in ["complex_analysis", "multi_step", "workflow"]
        )
        
        return {
            "requires_coordination": requires_coordination,
            "required_capabilities": required_capabilities,
            "suitable_agents": suitable_agents,
            "coordination_type": self._determine_coordination_type(task, required_capabilities)
        }
    
    async def _execute_coordinated_task(self, 
                                      task: Task, 
                                      coordination_plan: Dict[str, Any]) -> TaskResult:
        """
        Execute a task that requires coordination between multiple agents.
        
        Args:
            task: Task to execute
            coordination_plan: Plan for coordination
            
        Returns:
            Aggregated task result
        """
        coordination_type = coordination_plan["coordination_type"]
        
        if coordination_type == "sequential":
            return await self._execute_sequential_coordination(task, coordination_plan)
        elif coordination_type == "parallel":
            return await self._execute_parallel_coordination(task, coordination_plan)
        elif coordination_type == "hierarchical":
            return await self._execute_hierarchical_coordination(task, coordination_plan)
        else:
            # Default to capability-based delegation
            return await self._execute_capability_based_coordination(task, coordination_plan)
    
    async def _execute_single_agent_task(self, task: Task) -> TaskResult:
        """
        Execute a task using a single agent.
        
        Args:
            task: Task to execute
            
        Returns:
            Task result
        """
        # Delegate to the most suitable agent
        delegation_request = TaskDelegationRequest(
            task=task,
            required_capabilities=[],  # Will be determined by delegator
            priority=task.priority.value if hasattr(task.priority, 'value') else 1
        )
        
        delegation_response = await self.task_delegator.delegate_task(delegation_request)
        
        if not delegation_response.success:
            raise RuntimeError(f"Failed to delegate task: {delegation_response.error_message}")
        
        # Execute the task
        return await self.agent_manager.execute_task_with_agent(
            task, delegation_response.assigned_agent_id
        )
    
    async def _execute_sequential_coordination(self, 
                                             task: Task, 
                                             coordination_plan: Dict[str, Any]) -> TaskResult:
        """Execute coordination where agents work sequentially."""
        subtasks = await self._decompose_task_sequential(task)
        results = []
        
        for subtask in subtasks:
            # Each subtask depends on the previous one
            if results:
                # Pass previous results as context
                subtask.context.update({"previous_results": results})
            
            delegation_request = TaskDelegationRequest(
                task=subtask,
                required_capabilities=await self._extract_required_capabilities(subtask)
            )
            
            delegation_response = await self.task_delegator.delegate_task(delegation_request)
            
            if delegation_response.success:
                result = await self.agent_manager.execute_task_with_agent(
                    subtask, delegation_response.assigned_agent_id
                )
                results.append(result)
            else:
                # Handle delegation failure
                results.append(TaskResult(
                    task_id=subtask.id,
                    status=TaskStatus.FAILED,
                    error=delegation_response.error_message
                ))
        
        # Aggregate results
        return await self.result_aggregator.aggregate_sequential_results(task, results)
    
    async def _execute_parallel_coordination(self, 
                                           task: Task, 
                                           coordination_plan: Dict[str, Any]) -> TaskResult:
        """Execute coordination where agents work in parallel."""
        subtasks = await self._decompose_task_parallel(task)
        
        # Create delegation requests for all subtasks
        delegation_tasks = []
        for subtask in subtasks:
            delegation_request = TaskDelegationRequest(
                task=subtask,
                required_capabilities=await self._extract_required_capabilities(subtask)
            )
            delegation_tasks.append(self.task_delegator.delegate_task(delegation_request))
        
        # Execute delegations in parallel
        delegation_responses = await asyncio.gather(*delegation_tasks, return_exceptions=True)
        
        # Execute tasks in parallel
        execution_tasks = []
        for i, response in enumerate(delegation_responses):
            if isinstance(response, Exception):
                continue
            if response.success:
                execution_tasks.append(
                    self.agent_manager.execute_task_with_agent(
                        subtasks[i], response.assigned_agent_id
                    )
                )
        
        # Wait for all executions to complete
        results = await asyncio.gather(*execution_tasks, return_exceptions=True)
        
        # Convert exceptions to failed results
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append(TaskResult(
                    task_id=task.id,
                    status=TaskStatus.FAILED,
                    error=str(result)
                ))
            else:
                processed_results.append(result)
        
        # Aggregate results
        return await self.result_aggregator.aggregate_parallel_results(task, processed_results)
    
    async def _execute_hierarchical_coordination(self, 
                                               task: Task, 
                                               coordination_plan: Dict[str, Any]) -> TaskResult:
        """Execute coordination with hierarchical agent structure."""
        # Find a coordinator agent
        coordinator_agents = await self.agent_registry.find_agents_by_capability(
            AgentCapability.CODE_ANALYSIS  # Use as default coordinator capability
        )
        
        if not coordinator_agents:
            # Fallback to sequential coordination
            return await self._execute_sequential_coordination(task, coordination_plan)
        
        coordinator_id = coordinator_agents[0]
        
        # The coordinator agent will manage the subtasks
        coordination_task = Task(
            name=f"coordinate_{task.name}",
            description=f"Coordinate execution of {task.name}",
            task_type="coordination",
            parameters={
                "original_task": task.model_dump(),
                "coordination_plan": coordination_plan
            }
        )
        
        return await self.agent_manager.execute_task_with_agent(coordination_task, coordinator_id)
    
    async def _execute_capability_based_coordination(self, 
                                                   task: Task, 
                                                   coordination_plan: Dict[str, Any]) -> TaskResult:
        """Execute coordination based on agent capabilities."""
        required_capabilities = coordination_plan["required_capabilities"]
        
        # Group subtasks by capability
        capability_tasks = {}
        for capability in required_capabilities:
            capability_tasks[capability] = await self._create_capability_specific_task(task, capability)
        
        # Execute tasks for each capability
        results = []
        for capability, capability_task in capability_tasks.items():
            agents = await self.agent_registry.find_agents_by_capability(capability)
            if agents:
                result = await self.agent_manager.execute_task_with_agent(
                    capability_task, agents[0]
                )
                results.append(result)
        
        # Aggregate results
        return await self.result_aggregator.aggregate_capability_results(task, results)
    
    async def _extract_required_capabilities(self, task: Task) -> List[AgentCapability]:
        """Extract required capabilities from a task."""
        # This is a simplified implementation
        # In practice, this would use NLP or rule-based analysis
        
        capabilities = []
        
        task_lower = task.name.lower() + " " + task.description.lower()
        
        if any(word in task_lower for word in ["analyze", "analysis", "review", "inspect"]):
            capabilities.append(AgentCapability.CODE_ANALYSIS)
        
        if any(word in task_lower for word in ["test", "testing", "verify", "validate"]):
            capabilities.append(AgentCapability.TESTING)
        
        if any(word in task_lower for word in ["document", "documentation", "readme", "guide"]):
            capabilities.append(AgentCapability.DOCUMENTATION)
        
        if any(word in task_lower for word in ["refactor", "improve", "optimize", "clean"]):
            capabilities.append(AgentCapability.REFACTORING)
        
        if any(word in task_lower for word in ["error", "bug", "issue", "problem"]):
            capabilities.append(AgentCapability.ERROR_DETECTION)
        
        # Default to code analysis if no specific capability detected
        if not capabilities:
            capabilities.append(AgentCapability.CODE_ANALYSIS)
        
        return capabilities
    
    def _determine_coordination_type(self, 
                                   task: Task, 
                                   required_capabilities: List[AgentCapability]) -> str:
        """Determine the type of coordination needed."""
        if len(required_capabilities) == 1:
            return "single"
        elif task.task_type in ["workflow", "pipeline"]:
            return "sequential"
        elif task.task_type in ["analysis", "review"]:
            return "parallel"
        elif task.task_type in ["complex", "multi_step"]:
            return "hierarchical"
        else:
            return "capability_based"
    
    async def _decompose_task_sequential(self, task: Task) -> List[Task]:
        """Decompose a task into sequential subtasks."""
        # Simplified decomposition - in practice, this would be more sophisticated
        subtasks = []
        
        if "analyze" in task.name.lower():
            subtasks.append(Task(
                name=f"analyze_{task.name}",
                description=f"Analyze {task.description}",
                task_type="analysis",
                parameters=task.parameters
            ))
        
        if "test" in task.name.lower():
            subtasks.append(Task(
                name=f"test_{task.name}",
                description=f"Test {task.description}",
                task_type="testing",
                parameters=task.parameters
            ))
        
        if "document" in task.name.lower():
            subtasks.append(Task(
                name=f"document_{task.name}",
                description=f"Document {task.description}",
                task_type="documentation",
                parameters=task.parameters
            ))
        
        return subtasks or [task]  # Return original task if no decomposition
    
    async def _decompose_task_parallel(self, task: Task) -> List[Task]:
        """Decompose a task into parallel subtasks."""
        # Create subtasks that can be executed in parallel
        subtasks = []
        
        # Create analysis subtask
        subtasks.append(Task(
            name=f"analyze_{task.name}",
            description=f"Analyze {task.description}",
            task_type="analysis",
            parameters=task.parameters
        ))
        
        # Create quality check subtask
        subtasks.append(Task(
            name=f"quality_check_{task.name}",
            description=f"Quality check for {task.description}",
            task_type="quality_check",
            parameters=task.parameters
        ))
        
        return subtasks
    
    async def _create_capability_specific_task(self, 
                                             task: Task, 
                                             capability: AgentCapability) -> Task:
        """Create a task specific to a capability."""
        return Task(
            name=f"{capability.value}_{task.name}",
            description=f"Apply {capability.value} to {task.description}",
            task_type=capability.value,
            parameters=task.parameters
        )
    
    def _record_coordination_completion(self, coordination_id: UUID, result: TaskResult) -> None:
        """Record the completion of a coordination."""
        coordination_record = {
            "coordination_id": str(coordination_id),
            "result": result.model_dump(),
            "completed_at": result.completed_at
        }
        
        self._coordination_history.append(coordination_record)
        
        # Keep only last 1000 records
        if len(self._coordination_history) > 1000:
            self._coordination_history = self._coordination_history[-1000:]
