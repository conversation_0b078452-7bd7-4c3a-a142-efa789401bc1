"""
Analyze command for the CLI.

Provides code analysis capabilities including complexity analysis,
quality metrics, and pattern detection.
"""

import argparse
from typing import Any, Dict

from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator
from .base import (
    AsyncCommandBase, FileInputMixin, OutputFormatMixin, 
    PluginCommandMixin, ValidationMixin
)


class AnalyzeCommand(AsyncCommandBase, FileInputMixin, OutputFormatMixin, 
                    PluginCommandMixin, ValidationMixin):
    """Command for analyzing code quality, complexity, and patterns."""
    
    @property
    def name(self) -> str:
        """Get command name."""
        return "analyze"
    
    @property
    def description(self) -> str:
        """Get command description."""
        return "Analyze code for quality, complexity, and patterns"
    
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments."""
        # File input arguments
        self.add_file_arguments(parser)
        
        # Analysis type arguments
        analysis_group = parser.add_argument_group('Analysis Options')
        analysis_group.add_argument(
            '--type', '-t',
            choices=['complexity', 'quality', 'patterns', 'dependencies', 'all'],
            default='all',
            help='Type of analysis to perform (default: all)'
        )
        analysis_group.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed analysis results'
        )
        analysis_group.add_argument(
            '--threshold',
            type=int,
            default=10,
            help='Complexity threshold for warnings (default: 10)'
        )
        
        # Plugin arguments
        self.add_plugin_arguments(parser)
        
        # Output arguments
        self.add_output_arguments(parser)
    
    def get_help_text(self) -> str:
        """Get detailed help text."""
        return """
Analyze Python code for various quality metrics and patterns.

Analysis Types:
  complexity    - Cyclomatic complexity analysis
  quality       - Code quality metrics (maintainability, issues)
  patterns      - Design patterns and anti-patterns detection
  dependencies  - Import and dependency analysis
  all           - All analysis types (default)

Examples:
  agent-framework analyze --file code.py
  agent-framework analyze --code "def hello(): pass" --type complexity
  agent-framework analyze --file project.py --detailed --format json
  agent-framework analyze --stdin --type quality --threshold 15
        """
    
    async def execute(self, args: argparse.Namespace, 
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the analyze command."""
        try:
            # Handle plugin listing
            if args.list_plugins:
                return await self.list_plugins(orchestrator)
            
            # Get code input
            code = self.get_code_input(args)
            
            # Validate code
            if not self.validate_python_code(code):
                return {
                    "success": False,
                    "error": "Invalid Python code syntax"
                }
            
            # Perform analysis
            results = await self._perform_analysis(
                code, args.type, args.threshold, args.detailed, orchestrator
            )
            
            # Format and output results
            formatted_output = self._format_analysis_results(results, args)
            self.write_output(formatted_output, args.output)
            
            return {
                "success": True,
                "message": f"Analysis completed for {args.type} analysis",
                "results": results
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _perform_analysis(self, code: str, analysis_type: str, 
                               threshold: int, detailed: bool,
                               orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Perform the specified analysis."""
        results = {}
        
        if analysis_type in ['complexity', 'all']:
            complexity_result = await self.handle_plugin_request(
                orchestrator,
                'code_analysis',
                'analyze_complexity',
                {'code': code}
            )
            results['complexity'] = complexity_result
        
        if analysis_type in ['quality', 'all']:
            quality_result = await self.handle_plugin_request(
                orchestrator,
                'code_analysis',
                'analyze_quality',
                {'code': code}
            )
            results['quality'] = quality_result
        
        if analysis_type in ['patterns', 'all']:
            patterns_result = await self.handle_plugin_request(
                orchestrator,
                'code_analysis',
                'detect_patterns',
                {'code': code}
            )
            results['patterns'] = patterns_result
        
        if analysis_type in ['dependencies', 'all']:
            deps_result = await self.handle_plugin_request(
                orchestrator,
                'code_analysis',
                'analyze_dependencies',
                {'code': code}
            )
            results['dependencies'] = deps_result
        
        # Add summary and recommendations
        results['summary'] = self._generate_summary(results, threshold)
        
        return results
    
    def _generate_summary(self, results: Dict[str, Any], threshold: int) -> Dict[str, Any]:
        """Generate analysis summary."""
        summary = {
            "overall_score": 0,
            "issues": [],
            "recommendations": [],
            "metrics": {}
        }
        
        # Complexity analysis summary
        if 'complexity' in results and results['complexity'].get('success'):
            complexity_data = results['complexity'].get('result', {})
            avg_complexity = complexity_data.get('average_complexity', 0)
            
            summary["metrics"]["average_complexity"] = avg_complexity
            
            if avg_complexity > threshold:
                summary["issues"].append(
                    f"High average complexity: {avg_complexity:.1f} (threshold: {threshold})"
                )
                summary["recommendations"].append(
                    "Consider breaking down complex functions into smaller ones"
                )
        
        # Quality analysis summary
        if 'quality' in results and results['quality'].get('success'):
            quality_data = results['quality'].get('result', {})
            maintainability = quality_data.get('maintainability_index', 0)
            issues = quality_data.get('issues', [])
            
            summary["metrics"]["maintainability_index"] = maintainability
            summary["issues"].extend(issues)
            
            if maintainability < 50:
                summary["recommendations"].append(
                    "Improve code maintainability by adding comments and reducing complexity"
                )
        
        # Calculate overall score
        complexity_score = min(100, max(0, 100 - (summary["metrics"].get("average_complexity", 0) * 5)))
        maintainability_score = summary["metrics"].get("maintainability_index", 50)
        
        summary["overall_score"] = (complexity_score + maintainability_score) / 2
        
        return summary
    
    def _format_analysis_results(self, results: Dict[str, Any], 
                                args: argparse.Namespace) -> str:
        """Format analysis results for output."""
        if args.format == 'json':
            return self.format_output(results, 'json', args.pretty)
        elif args.format == 'yaml':
            return self.format_output(results, 'yaml', args.pretty)
        elif args.format == 'table':
            return self._format_as_table(results)
        else:
            return self._format_as_text(results, args.detailed)
    
    def _format_as_text(self, results: Dict[str, Any], detailed: bool) -> str:
        """Format results as human-readable text."""
        lines = []
        lines.append("Code Analysis Results")
        lines.append("=" * 50)
        
        # Summary
        if 'summary' in results:
            summary = results['summary']
            lines.append(f"\nOverall Score: {summary.get('overall_score', 0):.1f}/100")
            
            if summary.get('issues'):
                lines.append("\nIssues Found:")
                for issue in summary['issues']:
                    lines.append(f"  • {issue}")
            
            if summary.get('recommendations'):
                lines.append("\nRecommendations:")
                for rec in summary['recommendations']:
                    lines.append(f"  • {rec}")
        
        # Detailed results
        if detailed:
            for analysis_type, result in results.items():
                if analysis_type == 'summary':
                    continue
                
                lines.append(f"\n{analysis_type.title()} Analysis:")
                lines.append("-" * 30)
                
                if result.get('success'):
                    data = result.get('result', {})
                    for key, value in data.items():
                        lines.append(f"  {key}: {value}")
                else:
                    lines.append(f"  Error: {result.get('error', 'Unknown error')}")
        
        return "\n".join(lines)
    
    def _format_as_table(self, results: Dict[str, Any]) -> str:
        """Format results as table."""
        from ..utils import TableFormatter
        
        # Create summary table
        headers = ["Metric", "Value", "Status"]
        rows = []
        
        if 'summary' in results:
            summary = results['summary']
            metrics = summary.get('metrics', {})
            
            for metric, value in metrics.items():
                status = "Good" if isinstance(value, (int, float)) and value > 50 else "Needs Improvement"
                rows.append([metric.replace('_', ' ').title(), str(value), status])
        
        return TableFormatter.format_table(headers, rows, 'grid')
