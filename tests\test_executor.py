"""
Tests for the TaskExecutor component.
"""

import asyncio
import pytest
import time
from unittest.mock import patch, AsyncMock

from agent_framework.core.types import Task, TaskResult, TaskStatus, TaskPriority
from agent_framework.execution.executor import TaskExecutor


class TestTaskExecutor:
    """Test cases for TaskExecutor."""

    @pytest.mark.asyncio
    async def test_executor_initialization(self, task_executor):
        """Test that the executor initializes correctly."""
        assert task_executor._is_initialized
        assert task_executor._is_running
        assert len(task_executor._workers) == 2  # Based on test config
        assert task_executor._task_queue is not None

    @pytest.mark.asyncio
    async def test_execute_simple_task(self, task_executor, sample_task):
        """Test executing a simple task."""
        result = await task_executor.execute_task(sample_task)

        assert isinstance(result, TaskResult)
        assert result.task_id == sample_task.id
        assert result.status == TaskStatus.COMPLETED
        assert result.result is not None
        assert result.execution_time is not None
        assert result.execution_time > 0

    @pytest.mark.asyncio
    async def test_execute_code_analysis_task(self, task_executor, code_analysis_task):
        """Test executing a code analysis task."""
        result = await task_executor.execute_task(code_analysis_task)

        assert result.status == TaskStatus.COMPLETED
        assert "Code analysis completed" in result.result
        assert result.execution_time > 0

    @pytest.mark.asyncio
    async def test_task_priority_ordering(self, task_executor, sample_task, high_priority_task):
        """Test that high priority tasks are executed first."""
        # Create multiple tasks with different priorities
        low_priority_task = Task(
            name="Low Priority",
            task_type="test",
            priority=TaskPriority.LOW
        )

        # Submit tasks in reverse priority order
        tasks = [low_priority_task, sample_task, high_priority_task]
        results = await asyncio.gather(*[
            task_executor.execute_task(task) for task in tasks
        ])

        # All tasks should complete successfully
        for result in results:
            assert result.status == TaskStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, task_executor):
        """Test that multiple tasks can be executed concurrently."""
        # Create multiple tasks
        tasks = [
            Task(name=f"Task {i}", task_type="test", parameters={"id": i})
            for i in range(5)
        ]

        start_time = time.time()
        results = await asyncio.gather(*[
            task_executor.execute_task(task) for task in tasks
        ])
        end_time = time.time()

        # All tasks should complete
        assert len(results) == 5
        for result in results:
            assert result.status == TaskStatus.COMPLETED

        # Should take less time than sequential execution
        # (allowing for some overhead)
        assert end_time - start_time < 3.0

    @pytest.mark.asyncio
    async def test_task_timeout(self, test_config, message_broker):
        """Test task timeout functionality."""
        # Create executor with very short timeout
        test_config.execution.task_timeout_seconds = 0.1
        executor = TaskExecutor(test_config, message_broker)
        await executor.initialize()

        try:
            # Create a task that would take longer than timeout
            long_task = Task(
                name="Long Task",
                task_type="test",
                timeout_seconds=0.05  # Very short timeout
            )

            # Mock the task logic to take longer than timeout
            async def slow_task_logic(task):
                await asyncio.sleep(1.0)  # Takes 1 second, much longer than 0.05s timeout
                return "Should not reach here"

            with patch.object(executor, '_run_task_logic', side_effect=slow_task_logic):
                result = await executor.execute_task(long_task)

                assert result.status == TaskStatus.FAILED
                assert "timed out" in result.error.lower()

        finally:
            await executor.shutdown()

    @pytest.mark.asyncio
    async def test_task_error_handling(self, task_executor):
        """Test error handling in task execution."""
        # Mock the task logic to raise an exception
        async def failing_task_logic(task):
            raise ValueError("Test error")

        with patch.object(task_executor, '_run_task_logic', side_effect=failing_task_logic):
            error_task = Task(name="Error Task", task_type="test")
            result = await task_executor.execute_task(error_task)

            assert result.status == TaskStatus.FAILED
            assert "Test error" in result.error

    @pytest.mark.asyncio
    async def test_get_active_task_count(self, task_executor):
        """Test getting active task count."""
        initial_count = await task_executor.get_active_task_count()
        assert initial_count == 0

        # Test that the method works and returns an integer
        # The actual count may vary due to async execution timing
        assert isinstance(initial_count, int)
        assert initial_count >= 0

    @pytest.mark.asyncio
    async def test_get_queue_size(self, task_executor):
        """Test getting queue size."""
        initial_size = await task_executor.get_queue_size()
        assert initial_size == 0

        # The queue size is hard to test directly since tasks are processed quickly
        # This test mainly ensures the method works
        assert isinstance(initial_size, int)

    @pytest.mark.asyncio
    async def test_cancel_task(self, task_executor):
        """Test task cancellation."""
        # Test cancelling a non-existent task
        non_existent_id = "00000000-0000-0000-0000-000000000000"
        cancelled = await task_executor.cancel_task(non_existent_id)
        assert not cancelled  # Should return False for non-existent task

        # Test that the cancel_task method works
        task = Task(name="Test Task", task_type="test")
        # For a quick task, cancellation might not be possible, but method should work
        result = await task_executor.cancel_task(str(task.id))
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_task_retry_logic(self, task_executor):
        """Test task retry functionality."""
        # Note: Current executor implementation doesn't have retry logic
        # This test verifies that tasks with max_retries set still work

        async def failing_logic(task):
            raise ValueError("Task failure")

        with patch.object(task_executor, '_run_task_logic', side_effect=failing_logic):
            retry_task = Task(
                name="Retry Task",
                task_type="test",
                max_retries=2  # This parameter is accepted but not used yet
            )

            result = await task_executor.execute_task(retry_task)

            # Should fail since retry logic is not implemented yet
            assert result.status == TaskStatus.FAILED
            assert "Task failure" in result.error

    @pytest.mark.asyncio
    async def test_different_task_types(self, task_executor):
        """Test execution of different task types."""
        task_types = ["code_analysis", "refactoring", "test_generation", "generic"]

        for task_type in task_types:
            task = Task(
                name=f"{task_type} Task",
                task_type=task_type,
                parameters={"test": True}
            )

            result = await task_executor.execute_task(task)
            assert result.status == TaskStatus.COMPLETED
            assert result.result is not None

    @pytest.mark.asyncio
    async def test_task_metadata_preservation(self, task_executor, sample_task):
        """Test that task metadata is preserved through execution."""
        original_name = sample_task.name
        original_description = sample_task.description
        original_parameters = sample_task.parameters.copy()

        result = await task_executor.execute_task(sample_task)

        # Task metadata should be preserved
        assert sample_task.name == original_name
        assert sample_task.description == original_description
        assert sample_task.parameters == original_parameters

        # Task should have execution timestamps
        assert sample_task.started_at is not None
        assert sample_task.completed_at is not None
        assert sample_task.completed_at >= sample_task.started_at

    @pytest.mark.asyncio
    async def test_executor_shutdown(self, test_config, message_broker):
        """Test executor shutdown process."""
        executor = TaskExecutor(test_config, message_broker)
        await executor.initialize()

        assert executor._is_initialized
        assert executor._is_running

        await executor.shutdown()

        assert not executor._is_initialized
        assert not executor._is_running
        assert len(executor._workers) == 0
        assert len(executor._active_tasks) == 0