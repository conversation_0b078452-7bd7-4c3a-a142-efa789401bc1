"""
Integration tests for the agent framework.
"""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock

from agent_framework.core.orchestrator import AgentOrchestrator
from agent_framework.core.types import Task, TaskPriority


class TestIntegration:
    """Integration test cases."""
    
    @pytest.mark.asyncio
    async def test_basic_task_execution_flow(self, test_config):
        """Test basic task execution flow through the orchestrator."""
        # Mock external dependencies to avoid actual API calls
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient') as mock_client_class:
            with patch('agent_framework.core.orchestrator.AssistantAgent') as mock_agent_class:
                
                # Setup mock client
                mock_client = AsyncMock()
                mock_client_class.return_value = mock_client
                
                # Setup mock agent
                mock_agent = AsyncMock()
                mock_result = MagicMock()
                mock_result.messages = [MagicMock(content="Task completed successfully")]
                mock_agent.run.return_value = mock_result
                mock_agent_class.return_value = mock_agent
                
                # Create orchestrator
                orchestrator = AgentOrchestrator(test_config)
                
                try:
                    # Initialize the orchestrator
                    await orchestrator.initialize()
                    
                    # Verify initialization
                    assert orchestrator.is_initialized
                    assert orchestrator.is_running
                    
                    # Create a test task
                    task = Task(
                        name="Integration Test Task",
                        description="Test task for integration testing",
                        task_type="test",
                        priority=TaskPriority.NORMAL,
                        parameters={"test": True}
                    )
                    
                    # Execute the task
                    result = await orchestrator.execute_task(task)
                    
                    # Verify the result
                    assert result is not None
                    assert hasattr(result, 'status')
                    
                    # Test agent task execution
                    agent_response = await orchestrator.run_agent_task("Test agent task")
                    assert agent_response == "Task completed successfully"
                    
                    # Test metrics collection
                    metrics = await orchestrator.get_metrics()
                    assert metrics is not None
                    assert hasattr(metrics, 'cpu_usage')
                    assert hasattr(metrics, 'memory_usage')
                    
                finally:
                    # Clean shutdown
                    await orchestrator.shutdown()
                    assert not orchestrator.is_initialized
                    assert not orchestrator.is_running
    
    @pytest.mark.asyncio
    async def test_plugin_system_integration(self, test_config, mock_plugin):
        """Test plugin system integration."""
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                
                orchestrator = AgentOrchestrator(test_config)
                
                try:
                    await orchestrator.initialize()
                    
                    # Mock plugin loading
                    with patch.object(orchestrator._plugin_manager, 'load_plugin', return_value=mock_plugin):
                        loaded_plugin = await orchestrator.load_plugin("mock_plugin")
                        assert loaded_plugin == mock_plugin
                    
                finally:
                    await orchestrator.shutdown()
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, test_config):
        """Test error handling across the system."""
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                
                orchestrator = AgentOrchestrator(test_config)
                
                try:
                    await orchestrator.initialize()
                    
                    # Test task execution with error
                    task = Task(
                        name="Error Test Task",
                        task_type="test",
                        parameters={"cause_error": True}
                    )
                    
                    # Mock the executor to raise an error
                    with patch.object(orchestrator._task_executor, 'execute_task', side_effect=Exception("Test error")):
                        result = await orchestrator.execute_task(task)
                        
                        # Should handle error gracefully
                        assert result.status.value == "failed"
                        assert "Test error" in result.error
                
                finally:
                    await orchestrator.shutdown()
    
    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, test_config):
        """Test concurrent task execution."""
        import asyncio
        
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                
                orchestrator = AgentOrchestrator(test_config)
                
                try:
                    await orchestrator.initialize()
                    
                    # Create multiple tasks
                    tasks = [
                        Task(name=f"Concurrent Task {i}", task_type="test", parameters={"id": i})
                        for i in range(3)
                    ]
                    
                    # Execute tasks concurrently
                    results = await asyncio.gather(*[
                        orchestrator.execute_task(task) for task in tasks
                    ])
                    
                    # Verify all tasks completed
                    assert len(results) == 3
                    for result in results:
                        assert result is not None
                
                finally:
                    await orchestrator.shutdown()
    
    @pytest.mark.asyncio
    async def test_configuration_validation(self):
        """Test configuration validation."""
        from agent_framework.core.config import FrameworkConfig
        
        # Test valid configuration
        config = FrameworkConfig()
        config.model.api_key = "test-key"
        
        errors = config.validate_config()
        assert len(errors) == 0
        
        # Test invalid configuration
        invalid_config = FrameworkConfig()
        # Don't set API key
        
        errors = invalid_config.validate_config()
        assert len(errors) > 0
        assert any("API key" in error for error in errors)
    
    @pytest.mark.asyncio
    async def test_system_lifecycle(self, test_config):
        """Test complete system lifecycle."""
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                
                # Create orchestrator
                orchestrator = AgentOrchestrator(test_config)
                
                # Initial state
                assert not orchestrator.is_initialized
                assert not orchestrator.is_running
                
                # Initialize
                await orchestrator.initialize()
                assert orchestrator.is_initialized
                assert orchestrator.is_running
                
                # Use the system
                metrics = await orchestrator.get_metrics()
                assert metrics is not None
                
                # Shutdown
                await orchestrator.shutdown()
                assert not orchestrator.is_initialized
                assert not orchestrator.is_running
                
                # Should be able to shutdown again without error
                await orchestrator.shutdown()
