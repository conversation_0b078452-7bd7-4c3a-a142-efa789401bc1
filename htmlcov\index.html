<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">82%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-31 18:07 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_89f505f698d59396___init___py.html">agent_framework\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69___init___py.html">agent_framework\communication\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html">agent_framework\communication\broker.py</a></td>
                <td>67</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="48 67">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765___init___py.html">agent_framework\context\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html">agent_framework\context\manager.py</a></td>
                <td>75</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="35 75">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c___init___py.html">agent_framework\core\__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html">agent_framework\core\config.py</a></td>
                <td>126</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="123 126">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html">agent_framework\core\orchestrator.py</a></td>
                <td>177</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="167 177">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html">agent_framework\core\types.py</a></td>
                <td>117</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="108 117">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270___init___py.html">agent_framework\execution\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html">agent_framework\execution\executor.py</a></td>
                <td>138</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="126 138">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039___init___py.html">agent_framework\plugins\__init__.py</a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html">agent_framework\plugins\loader.py</a></td>
                <td>134</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="101 134">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html">agent_framework\plugins\manager.py</a></td>
                <td>142</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="87 142">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html">agent_framework\plugins\registry.py</a></td>
                <td>120</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="106 120">88%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1111</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="916 1111">82%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-31 18:07 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_34efe1c54516f039_registry_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_89f505f698d59396___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
