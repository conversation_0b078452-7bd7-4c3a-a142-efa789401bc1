"""
Programming Assistant Agent Framework

A comprehensive, high-performance programming assistant agent framework
with plugin-based functionality, built on top of AutoGen.
"""

from .core.orchestrator import AgentOrchestrator
from .core.config import FrameworkConfig
from .core.types import Task, TaskResult, TaskStatus, TaskPriority

__version__ = "0.1.0"
__all__ = [
    "AgentOrchestrator",
    "FrameworkConfig",
    "Task",
    "TaskResult",
    "TaskStatus",
    "TaskPriority",
]