"""
Specialized agent implementations for the multi-agent framework.
"""

from .base_agent import BaseAgent
from .code_analysis_agent import CodeAnalysisAgent
from .testing_agent import TestingAgent
from .documentation_agent import DocumentationAgent
from .refactoring_agent import RefactoringAgent
from .error_detection_agent import ErrorDetectionAgent
from .optimization_agent import OptimizationAgent

__all__ = [
    "BaseAgent",
    "CodeAnalysisAgent",
    "TestingAgent", 
    "DocumentationAgent",
    "RefactoringAgent",
    "ErrorDetectionAgent",
    "OptimizationAgent"
]
