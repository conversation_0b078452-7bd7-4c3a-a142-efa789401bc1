"""
Task executor for asynchronous task processing with priority queuing.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional
from datetime import datetime

from ..core.config import FrameworkConfig
from ..core.types import Task, TaskResult, TaskStatus, TaskPriority


class TaskExecutor:
    """
    Asynchronous task executor with priority queuing and error handling.

    Manages concurrent execution of tasks with proper resource management
    and error recovery.
    """

    def __init__(self, config: FrameworkConfig, message_broker=None):
        """Initialize the task executor."""
        self.config = config
        self.message_broker = message_broker
        self.logger = logging.getLogger(__name__)

        # Task management
        self._task_queue: asyncio.PriorityQueue = asyncio.PriorityQueue()
        self._active_tasks: Dict[str, asyncio.Task] = {}
        self._task_results: Dict[str, TaskResult] = {}

        # Worker management
        self._workers: List[asyncio.Task] = []
        self._is_running = False
        self._shutdown_event = asyncio.Event()

        self._is_initialized = False

    async def initialize(self) -> None:
        """Initialize the task executor."""
        if self._is_initialized:
            return

        self.logger.info("Initializing task executor...")

        # Start worker tasks
        self._is_running = True
        max_workers = self.config.execution.max_concurrent_tasks

        for i in range(max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self._workers.append(worker)

        self._is_initialized = True
        self.logger.info(f"Task executor initialized with {max_workers} workers")

    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a task and return the result."""
        if not self._is_initialized:
            raise RuntimeError("Task executor not initialized")

        self.logger.info(f"Queuing task: {task.name} ({task.id})")

        # Update task status
        task.status = TaskStatus.PENDING

        # Add to queue with priority
        priority = self._get_task_priority(task)
        await self._task_queue.put((priority, task))

        # Wait for task completion
        return await self._wait_for_task_completion(task)

    def _get_task_priority(self, task: Task) -> int:
        """Get numeric priority for task (lower number = higher priority)."""
        priority_map = {
            TaskPriority.CRITICAL: 1,
            TaskPriority.HIGH: 2,
            TaskPriority.NORMAL: 3,
            TaskPriority.LOW: 4
        }
        return priority_map.get(task.priority, 3)

    async def _wait_for_task_completion(self, task: Task) -> TaskResult:
        """Wait for a task to complete and return its result."""
        timeout = task.timeout_seconds or self.config.execution.task_timeout_seconds

        try:
            # Wait for task completion with timeout
            start_time = time.time()
            while str(task.id) not in self._task_results:
                if time.time() - start_time > timeout:
                    # Cancel the task if it's still active
                    if str(task.id) in self._active_tasks:
                        self._active_tasks[str(task.id)].cancel()

                    return TaskResult(
                        task_id=task.id,
                        status=TaskStatus.FAILED,
                        error=f"Task timed out after {timeout} seconds"
                    )

                await asyncio.sleep(0.1)

            return self._task_results[str(task.id)]

        except Exception as e:
            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )

    async def _worker(self, worker_name: str) -> None:
        """Worker coroutine that processes tasks from the queue."""
        self.logger.debug(f"Worker {worker_name} started")

        while self._is_running:
            try:
                # Get task from queue with timeout
                try:
                    priority, task = await asyncio.wait_for(
                        self._task_queue.get(),
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue

                # Execute the task
                await self._execute_single_task(task, worker_name)

            except Exception as e:
                self.logger.error(f"Worker {worker_name} error: {e}")

        self.logger.debug(f"Worker {worker_name} stopped")

    async def _execute_single_task(self, task: Task, worker_name: str) -> None:
        """Execute a single task."""
        task_id = str(task.id)

        try:
            self.logger.info(f"Worker {worker_name} executing task: {task.name}")

            # Update task status
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()

            # Create task execution coroutine
            execution_task = asyncio.create_task(self._run_task_logic(task))
            self._active_tasks[task_id] = execution_task

            # Execute the task
            start_time = time.time()
            result = await execution_task
            execution_time = time.time() - start_time

            # Create successful result
            task_result = TaskResult(
                task_id=task.id,
                status=TaskStatus.COMPLETED,
                result=result,
                execution_time=execution_time
            )

            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()

        except asyncio.CancelledError:
            # Task was cancelled
            task_result = TaskResult(
                task_id=task.id,
                status=TaskStatus.CANCELLED,
                error="Task was cancelled"
            )
            task.status = TaskStatus.CANCELLED

        except Exception as e:
            # Task failed
            self.logger.error(f"Task {task.name} failed: {e}")

            task_result = TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )
            task.status = TaskStatus.FAILED

        finally:
            # Clean up and store result
            if task_id in self._active_tasks:
                del self._active_tasks[task_id]

            self._task_results[task_id] = task_result

            # Clean up old results to prevent memory leaks
            await self._cleanup_old_results()

    async def _run_task_logic(self, task: Task) -> str:
        """Run the actual task logic."""
        # This is a placeholder implementation
        # In a real system, this would dispatch to appropriate handlers
        # based on task type

        if task.task_type == "code_analysis":
            return await self._run_code_analysis_task(task)
        elif task.task_type == "refactoring":
            return await self._run_refactoring_task(task)
        elif task.task_type == "test_generation":
            return await self._run_test_generation_task(task)
        else:
            # Generic task execution
            await asyncio.sleep(0.1)  # Simulate work
            return f"Task {task.name} completed successfully"

    async def _run_code_analysis_task(self, task: Task) -> str:
        """Run a code analysis task."""
        # Placeholder implementation
        await asyncio.sleep(0.5)  # Simulate analysis work
        return "Code analysis completed"

    async def _run_refactoring_task(self, task: Task) -> str:
        """Run a refactoring task."""
        # Placeholder implementation
        await asyncio.sleep(1.0)  # Simulate refactoring work
        return "Refactoring suggestions generated"

    async def _run_test_generation_task(self, task: Task) -> str:
        """Run a test generation task."""
        # Placeholder implementation
        await asyncio.sleep(0.8)  # Simulate test generation work
        return "Unit tests generated"

    async def _cleanup_old_results(self) -> None:
        """Clean up old task results to prevent memory leaks."""
        # Keep only the last 1000 results
        if len(self._task_results) > 1000:
            # Remove oldest results
            sorted_results = sorted(
                self._task_results.items(),
                key=lambda x: x[1].created_at
            )

            for task_id, _ in sorted_results[:-1000]:
                del self._task_results[task_id]

    async def get_active_task_count(self) -> int:
        """Get the number of currently active tasks."""
        return len(self._active_tasks)

    async def get_queue_size(self) -> int:
        """Get the current queue size."""
        return self._task_queue.qsize()

    async def cancel_task(self, task_id: str) -> bool:
        """Cancel an active task."""
        if task_id in self._active_tasks:
            self._active_tasks[task_id].cancel()
            return True
        return False

    async def shutdown(self) -> None:
        """Shutdown the task executor."""
        if not self._is_initialized:
            return

        self.logger.info("Shutting down task executor...")

        # Stop accepting new tasks
        self._is_running = False
        self._shutdown_event.set()

        # Cancel all active tasks
        for task_id, task in self._active_tasks.items():
            task.cancel()

        # Wait for workers to finish
        if self._workers:
            await asyncio.gather(*self._workers, return_exceptions=True)

        # Clear state
        self._active_tasks.clear()
        self._task_results.clear()
        self._workers.clear()

        self._is_initialized = False
        self.logger.info("Task executor shutdown complete")