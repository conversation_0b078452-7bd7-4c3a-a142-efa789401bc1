"""
Interactive CLI for the agent framework.

Provides an interactive command prompt with auto-completion,
command history, and real-time feedback.
"""

import asyncio
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

import colorama
from colorama import Fore, Style

from ..core.config import FrameworkConfig
from ..core.orchestrator import AgentOrchestrator
from .utils import CLIUtils


class InteractiveCLI:
    """Interactive command-line interface for the agent framework."""
    
    def __init__(self, orchestrator: AgentOrchestrator, config: FrameworkConfig,
                 show_banner: bool = True, history_file: Optional[str] = None):
        """Initialize interactive CLI."""
        self.orchestrator = orchestrator
        self.config = config
        self.show_banner = show_banner
        self.history_file = history_file or str(Path.home() / '.agent_framework_history')
        self.utils = CLIUtils()
        self.session_history: List[str] = []
        self.running = True
        
        # Try to import readline for better input handling
        try:
            import readline
            self.readline = readline
            self._setup_readline()
        except ImportError:
            self.readline = None
            self.utils.print_warning("Readline not available - limited input features")
    
    def _setup_readline(self) -> None:
        """Setup readline for command history and completion."""
        if not self.readline:
            return
        
        # Load history
        try:
            self.readline.read_history_file(self.history_file)
        except FileNotFoundError:
            pass  # No history file yet
        
        # Set up completion
        self.readline.set_completer(self._completer)
        self.readline.parse_and_bind('tab: complete')
        
        # Set history length
        self.readline.set_history_length(1000)
    
    def _completer(self, text: str, state: int) -> Optional[str]:
        """Auto-completion function."""
        commands = [
            'analyze', 'generate', 'optimize', 'debug', 'document',
            'help', 'exit', 'quit', 'clear', 'history', 'status'
        ]
        
        # Add subcommands based on context
        if text.startswith('analyze'):
            commands.extend(['analyze complexity', 'analyze quality', 'analyze patterns'])
        elif text.startswith('generate'):
            commands.extend(['generate function', 'generate class', 'generate boilerplate'])
        elif text.startswith('optimize'):
            commands.extend(['optimize performance', 'optimize memory', 'optimize algorithms'])
        elif text.startswith('debug'):
            commands.extend(['debug traceback', 'debug errors', 'debug syntax'])
        elif text.startswith('document'):
            commands.extend(['document docstrings', 'document api', 'document comments'])
        
        matches = [cmd for cmd in commands if cmd.startswith(text)]
        
        if state < len(matches):
            return matches[state]
        return None
    
    async def run(self) -> int:
        """Run the interactive CLI."""
        if self.show_banner:
            self._print_banner()
        
        self.utils.print_info("Type 'help' for available commands or 'exit' to quit")
        print()
        
        while self.running:
            try:
                # Get user input
                prompt = f"{Fore.GREEN}agent>{Style.RESET_ALL} "
                user_input = input(prompt).strip()
                
                if not user_input:
                    continue
                
                # Add to session history
                self.session_history.append(user_input)
                
                # Save to readline history
                if self.readline:
                    self.readline.add_history(user_input)
                
                # Process command
                await self._process_command(user_input)
                
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}Use 'exit' or 'quit' to leave{Style.RESET_ALL}")
                continue
            except EOFError:
                print(f"\n{Fore.YELLOW}Goodbye!{Style.RESET_ALL}")
                break
        
        # Save history
        if self.readline:
            try:
                self.readline.write_history_file(self.history_file)
            except Exception:
                pass  # Ignore history save errors
        
        return 0
    
    async def _process_command(self, command: str) -> None:
        """Process a user command."""
        parts = command.split()
        if not parts:
            return
        
        cmd = parts[0].lower()
        
        # Built-in commands
        if cmd in ['exit', 'quit']:
            self.running = False
            self.utils.print_info("Goodbye!")
            return
        
        elif cmd == 'help':
            self._show_help(parts[1:])
            return
        
        elif cmd == 'clear':
            self._clear_screen()
            return
        
        elif cmd == 'history':
            self._show_history()
            return
        
        elif cmd == 'status':
            await self._show_status()
            return
        
        # Framework commands
        elif cmd in ['analyze', 'generate', 'optimize', 'debug', 'document']:
            await self._execute_framework_command(command)
        
        # Natural language queries
        else:
            await self._handle_natural_language(command)
    
    def _show_help(self, args: List[str]) -> None:
        """Show help information."""
        if not args:
            help_text = f"""
{Fore.CYAN}Available Commands:{Style.RESET_ALL}

{Fore.GREEN}Framework Commands:{Style.RESET_ALL}
  analyze [type] [options]     - Analyze code for quality and complexity
  generate [type] [options]    - Generate code from specifications
  optimize [type] [options]    - Optimize code for performance
  debug [mode] [options]       - Debug and analyze errors
  document [type] [options]    - Generate documentation

{Fore.GREEN}Interactive Commands:{Style.RESET_ALL}
  help [command]               - Show help information
  status                       - Show framework status
  history                      - Show command history
  clear                        - Clear screen
  exit/quit                    - Exit interactive mode

{Fore.GREEN}Natural Language:{Style.RESET_ALL}
  You can also ask questions in natural language, such as:
  "How can I optimize this function?"
  "What's wrong with my code?"
  "Generate a class for user management"

{Fore.YELLOW}Type 'help <command>' for detailed information about a specific command{Style.RESET_ALL}
            """
            print(help_text)
        else:
            # Show help for specific command
            command = args[0]
            if command == 'analyze':
                print(f"{Fore.CYAN}Analyze Command:{Style.RESET_ALL}")
                print("  analyze complexity <code>    - Analyze cyclomatic complexity")
                print("  analyze quality <code>       - Analyze code quality metrics")
                print("  analyze patterns <code>      - Detect design patterns")
                print("  analyze dependencies <code>  - Analyze imports and dependencies")
            elif command == 'generate':
                print(f"{Fore.CYAN}Generate Command:{Style.RESET_ALL}")
                print("  generate function <spec>     - Generate a function")
                print("  generate class <spec>        - Generate a class")
                print("  generate boilerplate <type>  - Generate project boilerplate")
                print("  generate tests <code>        - Generate test cases")
            # Add more specific help as needed
            else:
                self.utils.print_warning(f"No specific help available for '{command}'")
    
    def _clear_screen(self) -> None:
        """Clear the screen."""
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
        if self.show_banner:
            self._print_banner()
    
    def _show_history(self) -> None:
        """Show command history."""
        if not self.session_history:
            self.utils.print_info("No commands in history")
            return
        
        print(f"{Fore.CYAN}Command History:{Style.RESET_ALL}")
        for i, cmd in enumerate(self.session_history[-10:], 1):  # Show last 10
            print(f"  {i:2d}. {cmd}")
    
    async def _show_status(self) -> None:
        """Show framework status."""
        print(f"{Fore.CYAN}Framework Status:{Style.RESET_ALL}")
        print(f"  Initialized: {'Yes' if self.orchestrator.is_initialized else 'No'}")
        print(f"  Running: {'Yes' if self.orchestrator.is_running else 'No'}")
        
        # Show plugin status
        if self.orchestrator._plugin_manager:
            plugins = self.orchestrator._plugin_manager.get_loaded_plugins()
            print(f"  Loaded Plugins: {len(plugins)}")
            for name in plugins.keys():
                print(f"    • {name}")
        
        # Show metrics if available
        try:
            metrics = await self.orchestrator.get_metrics()
            print(f"  CPU Usage: {metrics.cpu_usage:.1f}%")
            print(f"  Memory Usage: {metrics.memory_usage:.1f}%")
            print(f"  Active Tasks: {metrics.active_tasks}")
        except Exception:
            print("  Metrics: Not available")
    
    async def _execute_framework_command(self, command: str) -> None:
        """Execute a framework command."""
        try:
            # Parse command into arguments
            args = command.split()
            
            # Simple command routing
            if args[0] == 'analyze':
                await self._handle_analyze_command(args[1:])
            elif args[0] == 'generate':
                await self._handle_generate_command(args[1:])
            elif args[0] == 'optimize':
                await self._handle_optimize_command(args[1:])
            elif args[0] == 'debug':
                await self._handle_debug_command(args[1:])
            elif args[0] == 'document':
                await self._handle_document_command(args[1:])
            else:
                self.utils.print_error(f"Unknown command: {args[0]}")
        
        except Exception as e:
            self.utils.print_error(f"Command execution failed: {e}")
    
    async def _handle_analyze_command(self, args: List[str]) -> None:
        """Handle analyze command in interactive mode."""
        if not args:
            self.utils.print_warning("Usage: analyze <type> <code>")
            return
        
        analysis_type = args[0]
        if len(args) < 2:
            code = self.utils.prompt("Enter code to analyze")
        else:
            code = " ".join(args[1:])
        
        if not code:
            self.utils.print_error("No code provided")
            return
        
        # Execute analysis
        from .commands.analyze import AnalyzeCommand
        analyze_cmd = AnalyzeCommand()
        
        # Create mock args
        class MockArgs:
            def __init__(self):
                self.type = analysis_type if analysis_type in ['complexity', 'quality', 'patterns', 'dependencies'] else 'all'
                self.code = code
                self.file = None
                self.stdin = False
                self.detailed = True
                self.threshold = 10
                self.list_plugins = False
                self.format = 'text'
                self.output = None
                self.pretty = False
        
        result = await analyze_cmd.execute(MockArgs(), self.orchestrator, self.config)
        
        if result.get('success'):
            self.utils.print_success("Analysis completed")
        else:
            self.utils.print_error(f"Analysis failed: {result.get('error', 'Unknown error')}")
    
    async def _handle_generate_command(self, args: List[str]) -> None:
        """Handle generate command in interactive mode."""
        if not args:
            self.utils.print_warning("Usage: generate <type> [options]")
            return
        
        gen_type = args[0]
        
        if gen_type == 'function':
            name = self.utils.prompt("Function name")
            description = self.utils.prompt("Function description")
            
            # Simple function generation
            self.utils.print_info(f"Generating function '{name}'...")
            # Implementation would call the actual generate command
            
        elif gen_type == 'class':
            name = self.utils.prompt("Class name")
            description = self.utils.prompt("Class description")
            
            self.utils.print_info(f"Generating class '{name}'...")
            # Implementation would call the actual generate command
            
        else:
            self.utils.print_warning(f"Generation type '{gen_type}' not supported in interactive mode yet")
    
    async def _handle_optimize_command(self, args: List[str]) -> None:
        """Handle optimize command in interactive mode."""
        if not args:
            code = self.utils.prompt("Enter code to optimize")
        else:
            code = " ".join(args)
        
        if not code:
            self.utils.print_error("No code provided")
            return
        
        self.utils.print_info("Optimizing code...")
        # Implementation would call the actual optimize command
    
    async def _handle_debug_command(self, args: List[str]) -> None:
        """Handle debug command in interactive mode."""
        if not args:
            self.utils.print_warning("Usage: debug <mode> [options]")
            return
        
        debug_mode = args[0]
        
        if debug_mode == 'traceback':
            traceback_text = self.utils.prompt("Enter error traceback")
            self.utils.print_info("Analyzing traceback...")
            # Implementation would call the actual debug command
            
        elif debug_mode == 'errors':
            code = self.utils.prompt("Enter code to check for errors")
            self.utils.print_info("Checking for potential errors...")
            # Implementation would call the actual debug command
            
        else:
            self.utils.print_warning(f"Debug mode '{debug_mode}' not supported in interactive mode yet")
    
    async def _handle_document_command(self, args: List[str]) -> None:
        """Handle document command in interactive mode."""
        if not args:
            self.utils.print_warning("Usage: document <type> [options]")
            return
        
        doc_type = args[0]
        
        if doc_type == 'docstrings':
            code = self.utils.prompt("Enter code to document")
            style = self.utils.prompt("Docstring style (google/numpy/sphinx)", "google")
            
            self.utils.print_info("Generating docstrings...")
            # Implementation would call the actual document command
            
        else:
            self.utils.print_warning(f"Documentation type '{doc_type}' not supported in interactive mode yet")
    
    async def _handle_natural_language(self, query: str) -> None:
        """Handle natural language queries."""
        self.utils.print_info("Processing natural language query...")
        
        try:
            # Use the orchestrator's agent to handle the query
            response = await self.orchestrator.run_agent_task(query)
            
            print(f"\n{Fore.CYAN}Assistant:{Style.RESET_ALL}")
            print(response)
            print()
            
        except Exception as e:
            self.utils.print_error(f"Failed to process query: {e}")
    
    def _print_banner(self) -> None:
        """Print interactive mode banner."""
        banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                  Interactive Agent Framework                 ║
║                     Programming Assistant                    ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}

{Fore.GREEN}Welcome to the interactive programming assistant!{Style.RESET_ALL}
{Fore.YELLOW}You can use commands or ask questions in natural language.{Style.RESET_ALL}
        """
        print(banner)
