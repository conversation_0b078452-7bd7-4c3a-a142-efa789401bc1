"""
Generate command for the CLI.

Provides code generation capabilities including functions, classes,
boilerplate code, and templates.
"""

import argparse
from typing import Any, Dict, List

from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator
from .base import (
    AsyncCommandBase, OutputFormatMixin, PluginCommandMixin, ValidationMixin
)


class GenerateCommand(AsyncCommandBase, OutputFormatMixin, PluginCommandMixin, ValidationMixin):
    """Command for generating code from specifications."""
    
    @property
    def name(self) -> str:
        """Get command name."""
        return "generate"
    
    @property
    def description(self) -> str:
        """Get command description."""
        return "Generate code from specifications and templates"
    
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments."""
        # Generation type subcommands
        subparsers = parser.add_subparsers(
            dest='generate_type',
            help='Type of code to generate',
            metavar='TYPE'
        )
        
        # Function generation
        func_parser = subparsers.add_parser(
            'function',
            help='Generate a function'
        )
        self._add_function_arguments(func_parser)
        
        # Class generation
        class_parser = subparsers.add_parser(
            'class',
            help='Generate a class'
        )
        self._add_class_arguments(class_parser)
        
        # Boilerplate generation
        boilerplate_parser = subparsers.add_parser(
            'boilerplate',
            help='Generate boilerplate code'
        )
        self._add_boilerplate_arguments(boilerplate_parser)
        
        # Test generation
        test_parser = subparsers.add_parser(
            'tests',
            help='Generate test cases'
        )
        self._add_test_arguments(test_parser)
        
        # Plugin arguments (add to main parser)
        self.add_plugin_arguments(parser)
        
        # Output arguments (add to main parser)
        self.add_output_arguments(parser)
    
    def _add_function_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add function generation arguments."""
        parser.add_argument(
            '--name', '-n',
            required=True,
            help='Function name'
        )
        parser.add_argument(
            '--description', '-d',
            required=True,
            help='Function description'
        )
        parser.add_argument(
            '--parameters', '-p',
            nargs='*',
            help='Function parameters (name:type:default format)'
        )
        parser.add_argument(
            '--return-type', '-r',
            help='Return type annotation'
        )
        parser.add_argument(
            '--docstring-style',
            choices=['google', 'numpy', 'sphinx'],
            default='google',
            help='Docstring style (default: google)'
        )
        parser.add_argument(
            '--async',
            action='store_true',
            help='Generate async function'
        )
    
    def _add_class_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add class generation arguments."""
        parser.add_argument(
            '--name', '-n',
            required=True,
            help='Class name'
        )
        parser.add_argument(
            '--description', '-d',
            required=True,
            help='Class description'
        )
        parser.add_argument(
            '--attributes', '-a',
            nargs='*',
            help='Class attributes (name:type:default format)'
        )
        parser.add_argument(
            '--methods', '-m',
            nargs='*',
            help='Method specifications (name:description format)'
        )
        parser.add_argument(
            '--inheritance',
            nargs='*',
            help='Parent classes'
        )
        parser.add_argument(
            '--pattern',
            choices=['singleton', 'factory', 'observer', 'builder'],
            help='Design pattern to implement'
        )
    
    def _add_boilerplate_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add boilerplate generation arguments."""
        parser.add_argument(
            '--type', '-t',
            choices=['flask_app', 'fastapi_app', 'cli_app', 'pytest_test', 'django_model'],
            required=True,
            help='Type of boilerplate to generate'
        )
        parser.add_argument(
            '--name', '-n',
            required=True,
            help='Project/module name'
        )
        parser.add_argument(
            '--features', '-f',
            nargs='*',
            help='Additional features to include'
        )
        parser.add_argument(
            '--directory', '-D',
            help='Output directory for multiple files'
        )
    
    def _add_test_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add test generation arguments."""
        parser.add_argument(
            '--code', '-c',
            help='Code to generate tests for'
        )
        parser.add_argument(
            '--file', '-f',
            help='File to generate tests for'
        )
        parser.add_argument(
            '--framework',
            choices=['pytest', 'unittest'],
            default='pytest',
            help='Test framework (default: pytest)'
        )
        parser.add_argument(
            '--coverage',
            action='store_true',
            help='Generate comprehensive test coverage'
        )
    
    def get_help_text(self) -> str:
        """Get detailed help text."""
        return """
Generate various types of Python code from specifications.

Generation Types:
  function      - Generate functions with docstrings and type hints
  class         - Generate classes with methods and attributes
  boilerplate   - Generate project boilerplate (Flask, FastAPI, etc.)
  tests         - Generate test cases for existing code

Examples:
  agent-framework generate function --name calculate --description "Calculate sum"
  agent-framework generate class --name User --description "User model"
  agent-framework generate boilerplate --type flask_app --name myapp
  agent-framework generate tests --file mymodule.py --framework pytest
        """
    
    async def execute(self, args: argparse.Namespace, 
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the generate command."""
        try:
            # Handle plugin listing
            if args.list_plugins:
                return await self.list_plugins(orchestrator)
            
            # Check if generation type is specified
            if not args.generate_type:
                return {
                    "success": False,
                    "error": "No generation type specified. Use --help for available types."
                }
            
            # Execute specific generation
            if args.generate_type == 'function':
                result = await self._generate_function(args, orchestrator)
            elif args.generate_type == 'class':
                result = await self._generate_class(args, orchestrator)
            elif args.generate_type == 'boilerplate':
                result = await self._generate_boilerplate(args, orchestrator)
            elif args.generate_type == 'tests':
                result = await self._generate_tests(args, orchestrator)
            else:
                return {
                    "success": False,
                    "error": f"Unknown generation type: {args.generate_type}"
                }
            
            # Format and output results
            if result.get('success'):
                formatted_output = self._format_generation_results(result, args)
                self.write_output(formatted_output, args.output)
                
                return {
                    "success": True,
                    "message": f"Successfully generated {args.generate_type}",
                    "result": result
                }
            else:
                return result
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _generate_function(self, args: argparse.Namespace,
                                orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Generate a function."""
        # Parse parameters
        parameters = []
        if args.parameters:
            for param_spec in args.parameters:
                parts = param_spec.split(':')
                param = {"name": parts[0]}
                if len(parts) > 1:
                    param["type"] = parts[1]
                if len(parts) > 2:
                    param["default"] = parts[2]
                parameters.append(param)
        
        # Create request parameters
        request_params = {
            "name": args.name,
            "description": args.description,
            "parameters": parameters,
            "docstring_style": args.docstring_style
        }
        
        if args.return_type:
            request_params["return_type"] = args.return_type
        
        # Generate function
        return await self.handle_plugin_request(
            orchestrator,
            'code_generation',
            'generate_function',
            request_params
        )
    
    async def _generate_class(self, args: argparse.Namespace,
                             orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Generate a class."""
        # Parse attributes
        attributes = []
        if args.attributes:
            for attr_spec in args.attributes:
                parts = attr_spec.split(':')
                attr = {"name": parts[0]}
                if len(parts) > 1:
                    attr["type"] = parts[1]
                if len(parts) > 2:
                    attr["default"] = parts[2]
                attributes.append(attr)
        
        # Parse methods
        methods = []
        if args.methods:
            for method_spec in args.methods:
                parts = method_spec.split(':')
                method = {
                    "name": parts[0],
                    "description": parts[1] if len(parts) > 1 else f"Method {parts[0]}"
                }
                methods.append(method)
        
        # Create request parameters
        request_params = {
            "name": args.name,
            "description": args.description,
            "attributes": attributes,
            "methods": methods
        }
        
        if args.inheritance:
            request_params["inheritance"] = args.inheritance
        
        if args.pattern:
            request_params["design_pattern"] = args.pattern
        
        # Generate class
        return await self.handle_plugin_request(
            orchestrator,
            'code_generation',
            'generate_class',
            request_params
        )
    
    async def _generate_boilerplate(self, args: argparse.Namespace,
                                   orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Generate boilerplate code."""
        request_params = {
            "type": args.type,
            "name": args.name
        }
        
        if args.features:
            request_params["features"] = args.features
        
        # Generate boilerplate
        result = await self.handle_plugin_request(
            orchestrator,
            'code_generation',
            'generate_boilerplate',
            request_params
        )
        
        # Handle multiple files output
        if result.get('success') and args.directory:
            self._write_multiple_files(result.get('result', {}), args.directory)
        
        return result
    
    async def _generate_tests(self, args: argparse.Namespace,
                             orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Generate test cases."""
        # Get code input
        code = ""
        if args.file:
            from pathlib import Path
            file_path = Path(args.file)
            if not file_path.exists():
                return {"success": False, "error": f"File not found: {args.file}"}
            code = file_path.read_text(encoding='utf-8')
        elif args.code:
            code = args.code
        else:
            return {"success": False, "error": "No code provided for test generation"}
        
        request_params = {
            "code": code,
            "framework": args.framework
        }
        
        # Generate tests
        return await self.handle_plugin_request(
            orchestrator,
            'code_generation',
            'generate_tests',
            request_params
        )
    
    def _format_generation_results(self, result: Dict[str, Any], 
                                  args: argparse.Namespace) -> str:
        """Format generation results for output."""
        if not result.get('success'):
            return f"Generation failed: {result.get('error', 'Unknown error')}"
        
        data = result.get('result', {})
        
        if args.format == 'json':
            return self.format_output(data, 'json', args.pretty)
        elif args.format == 'yaml':
            return self.format_output(data, 'yaml', args.pretty)
        else:
            # Text format - show generated code
            if 'code' in data:
                return data['code']
            elif 'files' in data:
                # Multiple files
                output = []
                for filename, content in data['files'].items():
                    output.append(f"# File: {filename}")
                    output.append(content)
                    output.append("")
                return "\n".join(output)
            else:
                return str(data)
    
    def _write_multiple_files(self, result: Dict[str, Any], directory: str) -> None:
        """Write multiple generated files to directory."""
        from pathlib import Path
        
        output_dir = Path(directory)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        files = result.get('files', {})
        for filename, content in files.items():
            file_path = output_dir / filename
            file_path.write_text(content, encoding='utf-8')
            print(f"Generated: {file_path}")
