from typing import Literal

from pydantic import BaseModel
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import UserMessage

# The response format for the agent as a Pydantic base model.
class AgentResponse(BaseModel):
    thoughts: str
    response: Literal["happy", "sad", "neutral"]


# Create an agent that uses the OpenAI GPT-4o model with the custom response format.
model_client = OpenAIChatCompletionClient(
     model="qwen/qwen3-coder:free",
    api_key="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",
    base_url="https://openrouter.ai/api/v1",
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "unknown",
        "structured_output": True
    },  # type: ignore
    response_format=AgentResponse,  # type: ignore
)

async def run_agent():
    messages = [
        UserMessage(content="My mood is very general, I don't have any ideas", source="user"),
    ]
    response = await model_client.create(messages=messages)
    assert isinstance(response.content, str)
    parsed_response = AgentResponse.model_validate_json(response.content)
    print(parsed_response.thoughts)
    print(parsed_response.response)

    # Close the connection to the model client.
    await model_client.close()

if __name__ == "__main__":
    import asyncio
    asyncio.run(run_agent())