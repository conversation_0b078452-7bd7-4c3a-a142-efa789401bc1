"""
CLI utilities for the agent framework.

Provides utility functions for CLI operations including progress indicators,
colored output, and formatting helpers.
"""

import sys
import time
import threading
from contextlib import contextmanager
from typing import Any, Dict, List, Optional

import colorama
from colorama import Fore, Back, Style


class CLIUtils:
    """Utility functions for CLI operations."""
    
    def __init__(self):
        """Initialize CLI utilities."""
        colorama.init(autoreset=True)
    
    def print_success(self, message: str) -> None:
        """Print success message in green."""
        print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")
    
    def print_error(self, message: str) -> None:
        """Print error message in red."""
        print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}", file=sys.stderr)
    
    def print_warning(self, message: str) -> None:
        """Print warning message in yellow."""
        print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")
    
    def print_info(self, message: str) -> None:
        """Print info message in blue."""
        print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")
    
    def print_header(self, title: str) -> None:
        """Print section header."""
        print(f"\n{Fore.CYAN}{Style.BRIGHT}{'=' * 60}")
        print(f"{title.center(60)}")
        print(f"{'=' * 60}{Style.RESET_ALL}\n")
    
    def print_subheader(self, title: str) -> None:
        """Print subsection header."""
        print(f"\n{Fore.MAGENTA}{Style.BRIGHT}{title}")
        print(f"{'-' * len(title)}{Style.RESET_ALL}")
    
    def print_table(self, headers: List[str], rows: List[List[str]], 
                   title: Optional[str] = None) -> None:
        """Print formatted table."""
        if title:
            self.print_subheader(title)
        
        if not rows:
            self.print_info("No data to display")
            return
        
        # Calculate column widths
        col_widths = [len(header) for header in headers]
        for row in rows:
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    col_widths[i] = max(col_widths[i], len(str(cell)))
        
        # Print header
        header_row = " | ".join(
            f"{header:<{col_widths[i]}}" 
            for i, header in enumerate(headers)
        )
        print(f"{Fore.CYAN}{Style.BRIGHT}{header_row}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'-' * len(header_row)}{Style.RESET_ALL}")
        
        # Print rows
        for row in rows:
            formatted_row = " | ".join(
                f"{str(cell):<{col_widths[i]}}" 
                for i, cell in enumerate(row)
            )
            print(formatted_row)
    
    def print_code_block(self, code: str, language: str = "python", 
                        title: Optional[str] = None) -> None:
        """Print formatted code block."""
        if title:
            self.print_subheader(title)
        
        print(f"{Fore.BLACK}{Back.WHITE}```{language}")
        print(f"{code}")
        print(f"```{Style.RESET_ALL}")
    
    def print_json(self, data: Dict[str, Any], title: Optional[str] = None) -> None:
        """Print formatted JSON data."""
        import json
        
        if title:
            self.print_subheader(title)
        
        formatted_json = json.dumps(data, indent=2, ensure_ascii=False)
        print(f"{Fore.CYAN}{formatted_json}{Style.RESET_ALL}")
    
    def confirm(self, message: str, default: bool = False) -> bool:
        """Ask for user confirmation."""
        default_str = "Y/n" if default else "y/N"
        response = input(f"{Fore.YELLOW}? {message} ({default_str}): {Style.RESET_ALL}")
        
        if not response.strip():
            return default
        
        return response.lower().startswith('y')
    
    def prompt(self, message: str, default: Optional[str] = None) -> str:
        """Prompt user for input."""
        prompt_text = f"{Fore.YELLOW}? {message}"
        if default:
            prompt_text += f" ({default})"
        prompt_text += f": {Style.RESET_ALL}"
        
        response = input(prompt_text)
        return response.strip() or default or ""
    
    def select_option(self, message: str, options: List[str], 
                     default: Optional[int] = None) -> int:
        """Let user select from options."""
        print(f"{Fore.YELLOW}? {message}{Style.RESET_ALL}")
        
        for i, option in enumerate(options, 1):
            marker = f"{Fore.GREEN}→{Style.RESET_ALL}" if default == i else " "
            print(f"  {marker} {i}. {option}")
        
        while True:
            try:
                prompt_text = f"{Fore.YELLOW}Select option"
                if default:
                    prompt_text += f" (default: {default})"
                prompt_text += f": {Style.RESET_ALL}"
                
                response = input(prompt_text)
                
                if not response.strip() and default:
                    return default - 1
                
                choice = int(response) - 1
                if 0 <= choice < len(options):
                    return choice
                else:
                    self.print_error(f"Please enter a number between 1 and {len(options)}")
            
            except ValueError:
                self.print_error("Please enter a valid number")
            except KeyboardInterrupt:
                raise


class ProgressIndicator:
    """Progress indicator for long-running operations."""
    
    def __init__(self):
        """Initialize progress indicator."""
        self._stop_event = threading.Event()
        self._spinner_thread = None
        self._spinner_chars = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    
    @contextmanager
    def spinner(self, message: str):
        """Context manager for spinner progress indicator."""
        self.start_spinner(message)
        try:
            yield
        finally:
            self.stop_spinner()
    
    def start_spinner(self, message: str) -> None:
        """Start spinner with message."""
        self._stop_event.clear()
        self._spinner_thread = threading.Thread(
            target=self._spin,
            args=(message,),
            daemon=True
        )
        self._spinner_thread.start()
    
    def stop_spinner(self) -> None:
        """Stop spinner."""
        if self._spinner_thread:
            self._stop_event.set()
            self._spinner_thread.join()
            # Clear the line
            print(f"\r{' ' * 80}\r", end='', flush=True)
    
    def _spin(self, message: str) -> None:
        """Spinner animation loop."""
        i = 0
        while not self._stop_event.is_set():
            char = self._spinner_chars[i % len(self._spinner_chars)]
            print(f"\r{Fore.CYAN}{char} {message}{Style.RESET_ALL}", end='', flush=True)
            time.sleep(0.1)
            i += 1
    
    def progress_bar(self, current: int, total: int, message: str = "", 
                    width: int = 50) -> None:
        """Display progress bar."""
        if total == 0:
            percentage = 100
        else:
            percentage = min(100, (current * 100) // total)
        
        filled = int(width * percentage // 100)
        bar = f"{'█' * filled}{'░' * (width - filled)}"
        
        print(f"\r{Fore.GREEN}{bar}{Style.RESET_ALL} {percentage:3d}% {message}", 
              end='', flush=True)
        
        if current >= total:
            print()  # New line when complete
    
    def step_progress(self, step: int, total_steps: int, step_name: str) -> None:
        """Display step progress."""
        print(f"{Fore.BLUE}[{step}/{total_steps}]{Style.RESET_ALL} {step_name}")


class TableFormatter:
    """Utility for formatting tables with various styles."""
    
    @staticmethod
    def format_table(headers: List[str], rows: List[List[str]], 
                    style: str = "simple") -> str:
        """Format table with specified style."""
        if not rows:
            return "No data to display"
        
        # Calculate column widths
        col_widths = [len(header) for header in headers]
        for row in rows:
            for i, cell in enumerate(row):
                if i < len(col_widths):
                    col_widths[i] = max(col_widths[i], len(str(cell)))
        
        if style == "simple":
            return TableFormatter._format_simple(headers, rows, col_widths)
        elif style == "grid":
            return TableFormatter._format_grid(headers, rows, col_widths)
        elif style == "markdown":
            return TableFormatter._format_markdown(headers, rows, col_widths)
        else:
            return TableFormatter._format_simple(headers, rows, col_widths)
    
    @staticmethod
    def _format_simple(headers: List[str], rows: List[List[str]], 
                      col_widths: List[int]) -> str:
        """Format table in simple style."""
        lines = []
        
        # Header
        header_row = " | ".join(
            f"{header:<{col_widths[i]}}" 
            for i, header in enumerate(headers)
        )
        lines.append(header_row)
        lines.append("-" * len(header_row))
        
        # Rows
        for row in rows:
            formatted_row = " | ".join(
                f"{str(cell):<{col_widths[i]}}" 
                for i, cell in enumerate(row)
            )
            lines.append(formatted_row)
        
        return "\n".join(lines)
    
    @staticmethod
    def _format_grid(headers: List[str], rows: List[List[str]], 
                    col_widths: List[int]) -> str:
        """Format table in grid style."""
        lines = []
        
        # Top border
        border = "+" + "+".join("-" * (w + 2) for w in col_widths) + "+"
        lines.append(border)
        
        # Header
        header_row = "|" + "|".join(
            f" {header:<{col_widths[i]}} " 
            for i, header in enumerate(headers)
        ) + "|"
        lines.append(header_row)
        lines.append(border)
        
        # Rows
        for row in rows:
            formatted_row = "|" + "|".join(
                f" {str(cell):<{col_widths[i]}} " 
                for i, cell in enumerate(row)
            ) + "|"
            lines.append(formatted_row)
        
        # Bottom border
        lines.append(border)
        
        return "\n".join(lines)
    
    @staticmethod
    def _format_markdown(headers: List[str], rows: List[List[str]], 
                        col_widths: List[int]) -> str:
        """Format table in markdown style."""
        lines = []
        
        # Header
        header_row = "| " + " | ".join(
            f"{header:<{col_widths[i]}}" 
            for i, header in enumerate(headers)
        ) + " |"
        lines.append(header_row)
        
        # Separator
        separator = "| " + " | ".join("-" * w for w in col_widths) + " |"
        lines.append(separator)
        
        # Rows
        for row in rows:
            formatted_row = "| " + " | ".join(
                f"{str(cell):<{col_widths[i]}}" 
                for i, cell in enumerate(row)
            ) + " |"
            lines.append(formatted_row)
        
        return "\n".join(lines)
