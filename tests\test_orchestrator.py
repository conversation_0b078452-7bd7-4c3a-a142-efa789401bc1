"""
Tests for the AgentOrchestrator component.
"""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch

from agent_framework.core.orchestrator import AgentOrchestrator
from agent_framework.core.config import FrameworkConfig
from agent_framework.core.types import Task, TaskPriority, ContextQuery


class TestAgentOrchestrator:
    """Test cases for AgentOrchestrator."""

    @pytest.mark.asyncio
    async def test_orchestrator_initialization(self, test_config):
        """Test orchestrator initialization."""
        # Mock the model client to avoid actual API calls
        with patch('agent_framework.core.orchestrator.OpenAIChatCompletionClient'):
            with patch('agent_framework.core.orchestrator.AssistantAgent'):
                orchestrator = AgentOrchestrator(test_config)

                # Mock all the component initializations
                with patch.object(orchestrator, '_initialize_model_client'):
                    with patch.object(orchestrator, '_initialize_components'):
                        with patch.object(orchestrator, '_initialize_agent'):
                            with patch.object(orchestrator, '_load_plugins'):
                                await orchestrator.initialize()

                assert orchestrator.is_initialized
                assert orchestrator._start_time is not None

    @pytest.mark.asyncio
    async def test_orchestrator_initialization_failure(self, test_config):
        """Test orchestrator initialization failure handling."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock initialization to fail
        with patch.object(orchestrator, '_initialize_model_client', side_effect=Exception("Init failed")):
            with pytest.raises(Exception, match="Init failed"):
                await orchestrator.initialize()

            # Should not be initialized after failure
            assert not orchestrator.is_initialized

    @pytest.mark.asyncio
    async def test_execute_task_success(self, test_config, sample_task):
        """Test successful task execution."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock the task executor
        mock_executor = AsyncMock()
        mock_result = MagicMock()
        mock_result.status.value = "completed"
        mock_result.execution_time = 0.1
        mock_executor.execute_task.return_value = mock_result
        orchestrator._task_executor = mock_executor
        orchestrator._is_initialized = True

        # Mock message broker
        orchestrator._message_broker = AsyncMock()

        result = await orchestrator.execute_task(sample_task)

        assert result == mock_result
        mock_executor.execute_task.assert_called_once_with(sample_task)

    @pytest.mark.asyncio
    async def test_execute_task_not_initialized(self, test_config, sample_task):
        """Test task execution when not initialized."""
        orchestrator = AgentOrchestrator(test_config)

        with pytest.raises(RuntimeError, match="Framework not initialized"):
            await orchestrator.execute_task(sample_task)

    @pytest.mark.asyncio
    async def test_execute_task_no_executor(self, test_config, sample_task):
        """Test task execution when executor is not available."""
        orchestrator = AgentOrchestrator(test_config)
        orchestrator._is_initialized = True
        orchestrator._task_executor = None

        with pytest.raises(RuntimeError, match="Task executor not available"):
            await orchestrator.execute_task(sample_task)

    @pytest.mark.asyncio
    async def test_execute_task_failure(self, test_config, sample_task):
        """Test task execution failure handling."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock the task executor to raise an exception
        mock_executor = AsyncMock()
        mock_executor.execute_task.side_effect = Exception("Execution failed")
        orchestrator._task_executor = mock_executor
        orchestrator._is_initialized = True
        orchestrator._message_broker = AsyncMock()

        result = await orchestrator.execute_task(sample_task)

        assert result.status.value == "failed"
        assert "Execution failed" in result.error

    @pytest.mark.asyncio
    async def test_load_plugin(self, test_config):
        """Test plugin loading."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock the plugin manager
        mock_plugin_manager = AsyncMock()
        mock_plugin = MagicMock()
        mock_plugin_manager.load_plugin.return_value = mock_plugin
        orchestrator._plugin_manager = mock_plugin_manager

        result = await orchestrator.load_plugin("test_plugin")

        assert result == mock_plugin
        mock_plugin_manager.load_plugin.assert_called_once_with("test_plugin")

    @pytest.mark.asyncio
    async def test_load_plugin_no_manager(self, test_config):
        """Test plugin loading when manager is not available."""
        orchestrator = AgentOrchestrator(test_config)
        orchestrator._plugin_manager = None

        with pytest.raises(RuntimeError, match="Plugin manager not available"):
            await orchestrator.load_plugin("test_plugin")

    @pytest.mark.asyncio
    async def test_get_context(self, test_config):
        """Test context retrieval."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock the context manager
        mock_context_manager = AsyncMock()
        mock_context = MagicMock()
        mock_context_manager.get_context.return_value = mock_context
        orchestrator._context_manager = mock_context_manager

        query = ContextQuery(query_type="test", parameters={})
        result = await orchestrator.get_context(query)

        assert result == mock_context
        mock_context_manager.get_context.assert_called_once_with(query)

    @pytest.mark.asyncio
    async def test_get_context_no_manager(self, test_config):
        """Test context retrieval when manager is not available."""
        orchestrator = AgentOrchestrator(test_config)
        orchestrator._context_manager = None

        query = ContextQuery(query_type="test", parameters={})

        with pytest.raises(RuntimeError, match="Context manager not available"):
            await orchestrator.get_context(query)

    @pytest.mark.asyncio
    async def test_run_agent_task(self, test_config):
        """Test running agent task."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock the agent
        mock_agent = AsyncMock()
        mock_result = MagicMock()
        mock_result.messages = [MagicMock(content="Agent response")]
        mock_agent.run.return_value = mock_result
        orchestrator._agent = mock_agent

        result = await orchestrator.run_agent_task("Test task")

        assert result == "Agent response"
        mock_agent.run.assert_called_once_with(task="Test task")

    @pytest.mark.asyncio
    async def test_run_agent_task_no_agent(self, test_config):
        """Test running agent task when agent is not available."""
        orchestrator = AgentOrchestrator(test_config)
        orchestrator._agent = None

        with pytest.raises(RuntimeError, match="Agent not initialized"):
            await orchestrator.run_agent_task("Test task")

    @pytest.mark.asyncio
    async def test_run_agent_task_no_messages(self, test_config):
        """Test running agent task when no messages are returned."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock the agent to return empty messages
        mock_agent = AsyncMock()
        mock_result = MagicMock()
        mock_result.messages = []
        mock_agent.run.return_value = mock_result
        orchestrator._agent = mock_agent

        result = await orchestrator.run_agent_task("Test task")

        assert result == "No response generated"

    @pytest.mark.asyncio
    async def test_run_agent_task_failure(self, test_config):
        """Test agent task failure handling."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock the agent to raise an exception
        mock_agent = AsyncMock()
        mock_agent.run.side_effect = Exception("Agent failed")
        orchestrator._agent = mock_agent

        with pytest.raises(Exception, match="Agent failed"):
            await orchestrator.run_agent_task("Test task")

    @pytest.mark.asyncio
    async def test_get_metrics(self, test_config):
        """Test getting system metrics."""
        orchestrator = AgentOrchestrator(test_config)

        # Mock psutil
        with patch('psutil.cpu_percent') as mock_cpu, \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            mock_cpu.return_value = 50.0
            mock_memory.return_value.percent = 60.0
            mock_disk.return_value.percent = 70.0

            # Mock components
            mock_task_executor = AsyncMock()
            mock_task_executor.get_active_task_count.return_value = 5
            orchestrator._task_executor = mock_task_executor

            mock_plugin_manager = MagicMock()
            mock_plugin_manager.get_loaded_plugins.return_value = {"plugin1": None, "plugin2": None}
            orchestrator._plugin_manager = mock_plugin_manager

            metrics = await orchestrator.get_metrics()

            assert metrics.cpu_usage == 50.0
            assert metrics.memory_usage == 60.0
            assert metrics.disk_usage == 70.0
            assert metrics.active_tasks == 5
            assert metrics.plugin_count == 2

    @pytest.mark.asyncio
    async def test_get_metrics_no_components(self, test_config):
        """Test getting metrics when components are not available."""
        orchestrator = AgentOrchestrator(test_config)

        with patch('psutil.cpu_percent') as mock_cpu, \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            mock_cpu.return_value = 50.0
            mock_memory.return_value.percent = 60.0
            mock_disk.return_value.percent = 70.0

            # No components set
            orchestrator._task_executor = None
            orchestrator._plugin_manager = None

            metrics = await orchestrator.get_metrics()

            assert metrics.cpu_usage == 50.0
            assert metrics.memory_usage == 60.0
            assert metrics.disk_usage == 70.0
            assert metrics.active_tasks == 0
            assert metrics.plugin_count == 0

    @pytest.mark.asyncio
    async def test_shutdown(self, test_config):
        """Test orchestrator shutdown."""
        orchestrator = AgentOrchestrator(test_config)
        orchestrator._is_initialized = True
        orchestrator._start_time = 1000.0

        # Mock all components
        mock_plugin_manager = AsyncMock()
        mock_task_executor = AsyncMock()
        mock_context_manager = AsyncMock()
        mock_message_broker = AsyncMock()
        mock_model_client = AsyncMock()

        orchestrator._plugin_manager = mock_plugin_manager
        orchestrator._task_executor = mock_task_executor
        orchestrator._context_manager = mock_context_manager
        orchestrator._message_broker = mock_message_broker
        orchestrator._model_client = mock_model_client

        await orchestrator.shutdown()

        # Verify all components were shut down
        mock_plugin_manager.shutdown.assert_called_once()
        mock_task_executor.shutdown.assert_called_once()
        mock_context_manager.shutdown.assert_called_once()
        mock_message_broker.shutdown.assert_called_once()
        mock_model_client.close.assert_called_once()

        assert not orchestrator._is_initialized
        assert not orchestrator._is_running

    @pytest.mark.asyncio
    async def test_shutdown_not_initialized(self, test_config):
        """Test shutdown when not initialized."""
        orchestrator = AgentOrchestrator(test_config)
        orchestrator._is_initialized = False

        # Should not raise an exception
        await orchestrator.shutdown()

    @pytest.mark.asyncio
    async def test_shutdown_with_errors(self, test_config):
        """Test shutdown with component errors."""
        orchestrator = AgentOrchestrator(test_config)
        orchestrator._is_initialized = True

        # Mock component that raises an exception during shutdown
        mock_plugin_manager = AsyncMock()
        mock_plugin_manager.shutdown.side_effect = Exception("Shutdown error")
        orchestrator._plugin_manager = mock_plugin_manager
        orchestrator._message_broker = AsyncMock()

        # Should not raise an exception, just log the error
        await orchestrator.shutdown()

        assert not orchestrator._is_initialized

    def test_get_component_status(self, test_config):
        """Test getting component status."""
        orchestrator = AgentOrchestrator(test_config)

        # Set some components
        orchestrator._model_client = MagicMock()
        orchestrator._agent = MagicMock()
        orchestrator._plugin_manager = None  # Not set

        status = orchestrator._get_component_status()

        assert status["model_client"] is True
        assert status["agent"] is True
        assert status["plugin_manager"] is False
        assert status["context_manager"] is False
        assert status["task_executor"] is False
        assert status["message_broker"] is False

    def test_properties(self, test_config):
        """Test orchestrator properties."""
        orchestrator = AgentOrchestrator(test_config)

        # Initially not initialized
        assert not orchestrator.is_initialized
        assert not orchestrator.is_running

        # Set initialized state
        orchestrator._is_initialized = True
        orchestrator._is_running = True

        assert orchestrator.is_initialized
        assert orchestrator.is_running