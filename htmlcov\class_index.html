<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">82%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-31 18:07 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_89f505f698d59396___init___py.html">agent_framework\__init__.py</a></td>
                <td class="name left"><a href="z_89f505f698d59396___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69___init___py.html">agent_framework\communication\__init__.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t14">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html#t14"><data value='MessageBroker'>MessageBroker</data></a></td>
                <td>52</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="33 52">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html">agent_framework\communication\broker.py</a></td>
                <td class="name left"><a href="z_a8a5af316cde3c69_broker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765___init___py.html">agent_framework\context\__init__.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t13">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html#t13"><data value='ContextManager'>ContextManager</data></a></td>
                <td>58</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="18 58">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html">agent_framework\context\manager.py</a></td>
                <td class="name left"><a href="z_ce7a6f09b62e4765_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c___init___py.html">agent_framework\core\__init__.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t12">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t12"><data value='ModelConfig'>ModelConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t29">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t29"><data value='CacheConfig'>CacheConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t40">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t40"><data value='ExecutionConfig'>ExecutionConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t50">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t50"><data value='PluginConfig'>PluginConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t62">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t62"><data value='ContextConfig'>ContextConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t72">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t72"><data value='SecurityConfig'>SecurityConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t82">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t82"><data value='LoggingConfig'>LoggingConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t92">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t92"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t101">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html#t101"><data value='FrameworkConfig'>FrameworkConfig</data></a></td>
                <td>46</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="43 46">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html">agent_framework\core\config.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>80</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="80 80">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t34">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html#t34"><data value='AgentOrchestrator'>AgentOrchestrator</data></a></td>
                <td>140</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="132 140">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html">agent_framework\core\orchestrator.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_orchestrator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="35 37">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t16">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t16"><data value='TaskStatus'>TaskStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t25">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t25"><data value='TaskPriority'>TaskPriority</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t34">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t34"><data value='Task'>Task</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t71">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t71"><data value='TaskResult'>TaskResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t82">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t82"><data value='PluginCapability'>PluginCapability</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t92">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t92"><data value='PluginRequest'>PluginRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t100">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t100"><data value='PluginResponse'>PluginResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t109">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t109"><data value='ContextQuery'>ContextQuery</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t117">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t117"><data value='Context'>Context</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t125">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t125"><data value='AgentEvent'>AgentEvent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t133">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t133"><data value='ResourceMetrics'>ResourceMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t144">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html#t144"><data value='PluginInterface'>PluginInterface</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html">agent_framework\core\types.py</a></td>
                <td class="name left"><a href="z_528f5ab7e16d601c_types_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="104 104">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270___init___py.html">agent_framework\execution\__init__.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t15">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html#t15"><data value='TaskExecutor'>TaskExecutor</data></a></td>
                <td>114</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="102 114">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html">agent_framework\execution\executor.py</a></td>
                <td class="name left"><a href="z_8f23af4efcb45270_executor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039___init___py.html">agent_framework\plugins\__init__.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t17">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html#t17"><data value='PluginLoader'>PluginLoader</data></a></td>
                <td>114</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="81 114">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html">agent_framework\plugins\loader.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t15">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html#t15"><data value='PluginManager'>PluginManager</data></a></td>
                <td>113</td>
                <td>55</td>
                <td>0</td>
                <td class="right" data-ratio="58 113">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html">agent_framework\plugins\manager.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t14">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t14"><data value='PluginMetadata'>PluginMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t32">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html#t32"><data value='PluginRegistry'>PluginRegistry</data></a></td>
                <td>83</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="69 83">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html">agent_framework\plugins\registry.py</a></td>
                <td class="name left"><a href="z_34efe1c54516f039_registry_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1111</td>
                <td>195</td>
                <td>0</td>
                <td class="right" data-ratio="916 1111">82%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-07-31 18:07 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
