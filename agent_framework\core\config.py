"""
Configuration management for the agent framework.
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field, validator


class ModelProvider(str, Enum):
    """Supported model providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    OPENROUTER = "openrouter"
    LOCAL = "local"
    AZURE = "azure"


class ModelConfig(BaseModel):
    """Configuration for the AI model client."""
    provider: ModelProvider = ModelProvider.OPENROUTER
    model: str = "qwen/qwen3-coder:free"
    api_key: str = ""
    base_url: str = "https://openrouter.ai/api/v1"
    model_info: Dict[str, Any] = Field(default_factory=lambda: {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "unknown",
        "structured_output": True
    })
    max_tokens: int = 4096
    temperature: float = 0.7
    timeout_seconds: int = 30

    # Additional provider-specific settings
    azure_deployment: Optional[str] = None
    azure_api_version: Optional[str] = None
    local_model_path: Optional[str] = None


class CacheConfig(BaseModel):
    """Configuration for caching system."""
    enabled: bool = True
    cache_type: str = "memory"  # memory, disk, redis
    max_memory_size: int = 1024 * 1024 * 100  # 100MB
    disk_cache_dir: Optional[str] = None
    redis_url: Optional[str] = None
    ttl_seconds: int = 3600
    max_entries: int = 10000


class ExecutionConfig(BaseModel):
    """Configuration for task execution."""
    max_concurrent_tasks: int = 10
    task_timeout_seconds: int = 300
    max_retries: int = 3
    retry_delay_seconds: float = 1.0
    enable_task_queue: bool = True
    queue_max_size: int = 1000


class PluginConfig(BaseModel):
    """Configuration for plugin system."""
    plugin_directories: List[str] = Field(default_factory=lambda: ["plugins"])
    auto_load_plugins: bool = True
    plugin_timeout_seconds: int = 60
    max_plugin_memory: int = 1024 * 1024 * 50  # 50MB per plugin
    sandbox_enabled: bool = True
    allowed_imports: List[str] = Field(default_factory=lambda: [
        "os", "sys", "json", "re", "datetime", "pathlib", "typing"
    ])


class ContextConfig(BaseModel):
    """Configuration for context management."""
    context_store_type: str = "sqlite"  # sqlite, postgresql, memory
    database_url: Optional[str] = None
    max_context_size: int = 1024 * 1024 * 10  # 10MB
    context_retention_days: int = 30
    enable_semantic_search: bool = True
    embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"


class SecurityConfig(BaseModel):
    """Configuration for security settings."""
    enable_authentication: bool = False
    api_key_required: bool = False
    allowed_hosts: List[str] = Field(default_factory=lambda: ["localhost", "127.0.0.1"])
    max_request_size: int = 1024 * 1024 * 10  # 10MB
    rate_limit_requests: int = 100
    rate_limit_window_seconds: int = 60


class LoggingConfig(BaseModel):
    """Configuration for logging."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 1024 * 1024 * 10  # 10MB
    backup_count: int = 5
    enable_structured_logging: bool = True


class MonitoringConfig(BaseModel):
    """Configuration for monitoring and metrics."""
    enabled: bool = True
    metrics_endpoint: str = "/metrics"
    health_check_endpoint: str = "/health"
    collect_performance_metrics: bool = True
    metrics_retention_days: int = 7


class MCPServerConfig(BaseModel):
    """Configuration for an MCP server."""
    command: str
    args: List[str] = Field(default_factory=list)
    env: Dict[str, str] = Field(default_factory=dict)
    timeout_seconds: int = 30
    retry_attempts: int = 3
    retry_delay_seconds: float = 1.0
    description: Optional[str] = None
    enabled: bool = True


class MCPConfig(BaseModel):
    """Configuration for MCP integration."""
    servers: Dict[str, MCPServerConfig] = Field(default_factory=dict)
    connection_timeout: int = 30
    request_timeout: int = 60
    max_concurrent_connections: int = 10
    enable_server_discovery: bool = True
    discovery_timeout: int = 10


class AgentRoleConfig(BaseModel):
    """Configuration for agent role specialization."""
    name: str
    description: str
    system_message: str
    capabilities: List[str] = Field(default_factory=list)
    model_config: Optional[ModelConfig] = None
    mcp_servers: List[str] = Field(default_factory=list)
    max_concurrent_tasks: int = 5
    priority: int = 1  # 1=low, 2=normal, 3=high, 4=critical


class MultiAgentConfig(BaseModel):
    """Configuration for multi-agent coordination."""
    enabled: bool = False
    max_agents: int = 10
    coordination_strategy: str = "round_robin"  # round_robin, priority, load_balanced
    task_delegation_enabled: bool = True
    result_sharing_enabled: bool = True
    conflict_resolution_strategy: str = "voting"  # voting, priority, consensus
    agent_roles: Dict[str, AgentRoleConfig] = Field(default_factory=dict)
    communication_timeout: int = 30
    coordination_interval: float = 1.0


class FrameworkConfig(BaseModel):
    """Main configuration for the agent framework."""
    # Core settings
    name: str = "Programming Assistant Agent"
    version: str = "0.1.0"
    debug: bool = False

    # Component configurations
    model: ModelConfig = Field(default_factory=ModelConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    execution: ExecutionConfig = Field(default_factory=ExecutionConfig)
    plugins: PluginConfig = Field(default_factory=PluginConfig)
    context: ContextConfig = Field(default_factory=ContextConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)

    # Multi-agent and MCP configurations
    mcp: MCPConfig = Field(default_factory=MCPConfig)
    multi_agent: MultiAgentConfig = Field(default_factory=MultiAgentConfig)

    # Custom settings
    custom_settings: Dict[str, Any] = Field(default_factory=dict)

    @classmethod
    def from_file(cls, config_path: str) -> "FrameworkConfig":
        """Load configuration from a file."""
        import json
        import yaml

        path = Path(config_path)
        if not path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        with open(path, 'r', encoding='utf-8') as f:
            if path.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            elif path.suffix.lower() == '.json':
                data = json.load(f)
            else:
                raise ValueError(f"Unsupported configuration file format: {path.suffix}")

        return cls(**data)

    @classmethod
    def from_env(cls) -> "FrameworkConfig":
        """Load configuration from environment variables."""
        config = cls()

        # Model configuration
        if api_key := os.getenv("AGENT_API_KEY"):
            config.model.api_key = api_key
        if model := os.getenv("AGENT_MODEL"):
            config.model.model = model
        if base_url := os.getenv("AGENT_BASE_URL"):
            config.model.base_url = base_url

        # Cache configuration
        if cache_type := os.getenv("AGENT_CACHE_TYPE"):
            config.cache.cache_type = cache_type
        if redis_url := os.getenv("AGENT_REDIS_URL"):
            config.cache.redis_url = redis_url

        # Debug mode
        if debug := os.getenv("AGENT_DEBUG"):
            config.debug = debug.lower() in ("true", "1", "yes")

        return config

    def to_file(self, config_path: str) -> None:
        """Save configuration to a file."""
        import json
        import yaml

        path = Path(config_path)
        path.parent.mkdir(parents=True, exist_ok=True)

        data = self.model_dump()

        with open(path, 'w', encoding='utf-8') as f:
            if path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(data, f, default_flow_style=False)
            elif path.suffix.lower() == '.json':
                json.dump(data, f, indent=2)
            else:
                raise ValueError(f"Unsupported configuration file format: {path.suffix}")

    def validate_config(self) -> List[str]:
        """Validate the configuration and return any errors."""
        errors = []

        # Validate model configuration
        if not self.model.api_key:
            errors.append("Model API key is required")

        # Validate cache configuration
        if self.cache.cache_type == "redis" and not self.cache.redis_url:
            errors.append("Redis URL is required when using Redis cache")

        # Validate plugin directories
        for plugin_dir in self.plugins.plugin_directories:
            if not Path(plugin_dir).exists():
                errors.append(f"Plugin directory does not exist: {plugin_dir}")

        # Validate MCP server configurations
        for server_name, server_config in self.mcp.servers.items():
            if not server_config.command:
                errors.append(f"MCP server '{server_name}' missing command")

        # Validate multi-agent configuration
        if self.multi_agent.enabled:
            if self.multi_agent.max_agents < 1:
                errors.append("Multi-agent max_agents must be at least 1")

            # Validate agent role configurations
            for role_name, role_config in self.multi_agent.agent_roles.items():
                if not role_config.name:
                    errors.append(f"Agent role '{role_name}' missing name")
                if not role_config.system_message:
                    errors.append(f"Agent role '{role_name}' missing system_message")

                # Validate MCP servers referenced by agent roles
                for server_name in role_config.mcp_servers:
                    if server_name not in self.mcp.servers:
                        errors.append(f"Agent role '{role_name}' references unknown MCP server '{server_name}'")

        return errors