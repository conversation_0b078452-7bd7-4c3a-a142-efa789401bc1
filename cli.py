#!/usr/bin/env python3
"""
Main CLI entry point for the agent framework.

This script provides the command-line interface for the comprehensive
programming assistant agent framework.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from agent_framework.cli.core import main

if __name__ == '__main__':
    sys.exit(main())
