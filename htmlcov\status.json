{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "a8ac056e9c65337e7a539f974288606f", "files": {"z_89f505f698d59396___init___py": {"hash": "15d4f973e6a33eec0f97abc03b337fb0", "index": {"url": "z_89f505f698d59396___init___py.html", "file": "agent_framework\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a8a5af316cde3c69___init___py": {"hash": "3ebb889486ec1f893bb777ab9ef0b374", "index": {"url": "z_a8a5af316cde3c69___init___py.html", "file": "agent_framework\\communication\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a8a5af316cde3c69_broker_py": {"hash": "a2ffade72fffc152d611b3081d7272dd", "index": {"url": "z_a8a5af316cde3c69_broker_py.html", "file": "agent_framework\\communication\\broker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce7a6f09b62e4765___init___py": {"hash": "53bd25f7c81f175f794e021d38be6bd0", "index": {"url": "z_ce7a6f09b62e4765___init___py.html", "file": "agent_framework\\context\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ce7a6f09b62e4765_manager_py": {"hash": "8408eb2443239f14a58d04179f49dab4", "index": {"url": "z_ce7a6f09b62e4765_manager_py.html", "file": "agent_framework\\context\\manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 75, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_528f5ab7e16d601c___init___py": {"hash": "3c77fc9ef7f887ac2508d4109cf92472", "index": {"url": "z_528f5ab7e16d601c___init___py.html", "file": "agent_framework\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_528f5ab7e16d601c_config_py": {"hash": "df4490e14c6531bc8c8b05067195c327", "index": {"url": "z_528f5ab7e16d601c_config_py.html", "file": "agent_framework\\core\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 126, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_528f5ab7e16d601c_orchestrator_py": {"hash": "a949dea585fddbe68e0ce6f696038648", "index": {"url": "z_528f5ab7e16d601c_orchestrator_py.html", "file": "agent_framework\\core\\orchestrator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_528f5ab7e16d601c_types_py": {"hash": "15bb7416f2ede84188475f57402030d6", "index": {"url": "z_528f5ab7e16d601c_types_py.html", "file": "agent_framework\\core\\types.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 9, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f23af4efcb45270___init___py": {"hash": "1123a634cbb8d00d57becb72f1932ab2", "index": {"url": "z_8f23af4efcb45270___init___py.html", "file": "agent_framework\\execution\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f23af4efcb45270_executor_py": {"hash": "e2275d96349427c9007f63a6086bfea1", "index": {"url": "z_8f23af4efcb45270_executor_py.html", "file": "agent_framework\\execution\\executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_34efe1c54516f039___init___py": {"hash": "521b091eb90177bf252e245965931660", "index": {"url": "z_34efe1c54516f039___init___py.html", "file": "agent_framework\\plugins\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_34efe1c54516f039_loader_py": {"hash": "89bc802269c34a475bad0af5c3f31d44", "index": {"url": "z_34efe1c54516f039_loader_py.html", "file": "agent_framework\\plugins\\loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_34efe1c54516f039_manager_py": {"hash": "165390321a261c327cd7ccbfbd277a65", "index": {"url": "z_34efe1c54516f039_manager_py.html", "file": "agent_framework\\plugins\\manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_34efe1c54516f039_registry_py": {"hash": "405c92c9c156ba693b0cc69525eb7675", "index": {"url": "z_34efe1c54516f039_registry_py.html", "file": "agent_framework\\plugins\\registry.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}