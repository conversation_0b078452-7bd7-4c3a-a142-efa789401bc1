"""
Agent manager for coordinating multiple agents in the framework.
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

from .multi_agent_types import (
    AgentInfo, AgentStatus, AgentCapability, AgentInterface,
    TaskDelegationRequest, TaskDelegationResponse, AgentCommunicationMessage,
    ConflictResolutionRequest, ConflictResolutionResult, CoordinationStrategy,
    ConflictResolutionStrategy, CoordinationContext, MultiAgentEvent
)
from .agent_registry import AgentRegistry
from .types import Task, TaskResult, TaskStatus
from ..communication.broker import MessageBroker


class AgentManager:
    """
    Manager for coordinating multiple agents in the framework.
    
    Handles task delegation, result sharing, conflict resolution,
    and inter-agent communication.
    """
    
    def __init__(self, 
                 registry: AgentRegistry,
                 message_broker: MessageBroker,
                 coordination_strategy: CoordinationStrategy = CoordinationStrategy.ROUND_ROBIN):
        """
        Initialize the agent manager.
        
        Args:
            registry: The agent registry
            message_broker: The message broker for communication
            coordination_strategy: The strategy for coordinating agents
        """
        self.logger = logging.getLogger(__name__)
        self.registry = registry
        self.message_broker = message_broker
        self.coordination_strategy = coordination_strategy
        
        # Coordination state
        self.coordination_context = CoordinationContext(
            coordination_strategy=coordination_strategy
        )
        
        # Task management
        self._active_tasks: Dict[UUID, Task] = {}
        self._task_assignments: Dict[UUID, UUID] = {}  # task_id -> agent_id
        self._agent_tasks: Dict[UUID, Set[UUID]] = {}  # agent_id -> set of task_ids
        
        # Communication
        self._message_queue: asyncio.Queue = asyncio.Queue()
        self._conflict_resolution_queue: asyncio.Queue = asyncio.Queue()
        
        # Coordination loop
        self._coordination_task: Optional[asyncio.Task] = None
        self._running = False
        
        # Round-robin state
        self._last_assigned_agent_index = 0
    
    async def start(self) -> None:
        """Start the agent manager."""
        if self._running:
            return
        
        self.logger.info("Starting agent manager...")
        self._running = True
        
        # Start coordination loop
        self._coordination_task = asyncio.create_task(self._coordination_loop())
        
        self.logger.info("Agent manager started")
    
    async def stop(self) -> None:
        """Stop the agent manager."""
        if not self._running:
            return
        
        self.logger.info("Stopping agent manager...")
        self._running = False
        
        # Cancel coordination loop
        if self._coordination_task:
            self._coordination_task.cancel()
            try:
                await self._coordination_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Agent manager stopped")
    
    async def delegate_task(self, request: TaskDelegationRequest) -> TaskDelegationResponse:
        """
        Delegate a task to an appropriate agent.
        
        Args:
            request: The task delegation request
            
        Returns:
            The delegation response
        """
        try:
            self.logger.info(f"Delegating task: {request.task.name}")
            
            # Find suitable agents
            suitable_agents = await self._find_suitable_agents(
                request.required_capabilities,
                request.preferred_agent_id
            )
            
            if not suitable_agents:
                return TaskDelegationResponse(
                    success=False,
                    error_message="No suitable agents available"
                )
            
            # Select agent based on coordination strategy
            selected_agent_id = await self._select_agent(suitable_agents)
            
            if not selected_agent_id:
                return TaskDelegationResponse(
                    success=False,
                    error_message="Failed to select agent"
                )
            
            # Assign task to agent
            await self._assign_task_to_agent(request.task, selected_agent_id)
            
            # Estimate completion time (simplified)
            estimated_time = await self._estimate_completion_time(
                request.task, selected_agent_id
            )
            
            return TaskDelegationResponse(
                success=True,
                assigned_agent_id=selected_agent_id,
                estimated_completion_time=estimated_time
            )
            
        except Exception as e:
            self.logger.error(f"Error delegating task: {e}")
            return TaskDelegationResponse(
                success=False,
                error_message=str(e)
            )
    
    async def execute_task_with_agent(self, task: Task, agent_id: UUID) -> TaskResult:
        """
        Execute a task with a specific agent.
        
        Args:
            task: The task to execute
            agent_id: The ID of the agent to execute the task
            
        Returns:
            The task result
        """
        agent = await self.registry.get_agent(agent_id)
        if not agent:
            raise ValueError(f"Agent not found: {agent_id}")
        
        try:
            # Update agent status
            await self.registry.update_agent_status(agent_id, AgentStatus.BUSY)
            
            # Execute task
            result = await agent.execute_task(task)
            
            # Update metrics
            await self._update_agent_metrics(agent_id, result)
            
            return result
            
        finally:
            # Update agent status back to idle
            await self.registry.update_agent_status(agent_id, AgentStatus.IDLE)
    
    async def send_message(self, message: AgentCommunicationMessage) -> bool:
        """
        Send a message between agents.
        
        Args:
            message: The message to send
            
        Returns:
            True if the message was sent successfully
        """
        try:
            if message.recipient_id:
                # Send to specific agent
                recipient = await self.registry.get_agent(message.recipient_id)
                if recipient:
                    response = await recipient.handle_message(message)
                    if response and message.requires_response:
                        await self._message_queue.put(response)
                    return True
            else:
                # Broadcast to all agents
                all_agents = await self.registry.get_all_agents()
                for agent_id in all_agents:
                    agent = await self.registry.get_agent(agent_id)
                    if agent and agent_id != message.sender_id:
                        await agent.handle_message(message)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            return False
    
    async def resolve_conflict(self, request: ConflictResolutionRequest) -> ConflictResolutionResult:
        """
        Resolve a conflict between agents.
        
        Args:
            request: The conflict resolution request
            
        Returns:
            The conflict resolution result
        """
        try:
            self.logger.info(f"Resolving conflict: {request.conflict_id}")
            
            if request.resolution_strategy == ConflictResolutionStrategy.PRIORITY:
                return await self._resolve_by_priority(request)
            elif request.resolution_strategy == ConflictResolutionStrategy.VOTING:
                return await self._resolve_by_voting(request)
            elif request.resolution_strategy == ConflictResolutionStrategy.CONSENSUS:
                return await self._resolve_by_consensus(request)
            elif request.resolution_strategy == ConflictResolutionStrategy.FIRST_WINS:
                return await self._resolve_first_wins(request)
            else:
                raise ValueError(f"Unknown resolution strategy: {request.resolution_strategy}")
                
        except Exception as e:
            self.logger.error(f"Error resolving conflict: {e}")
            return ConflictResolutionResult(
                conflict_id=request.conflict_id,
                resolution="error",
                resolution_data={"error": str(e)}
            )
    
    async def get_coordination_status(self) -> Dict[str, Any]:
        """
        Get the current coordination status.
        
        Returns:
            Dictionary containing coordination status information
        """
        registry_stats = await self.registry.get_registry_stats()
        
        return {
            "coordination_strategy": self.coordination_strategy.value,
            "active_tasks": len(self._active_tasks),
            "task_assignments": len(self._task_assignments),
            "registry_stats": registry_stats,
            "coordination_context": self.coordination_context.dict(),
            "running": self._running
        }
    
    async def _coordination_loop(self) -> None:
        """Main coordination loop."""
        while self._running:
            try:
                # Process pending messages
                await self._process_messages()
                
                # Process conflict resolution requests
                await self._process_conflict_resolutions()
                
                # Update coordination context
                await self._update_coordination_context()
                
                # Sleep for coordination interval
                await asyncio.sleep(1.0)  # 1 second interval
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in coordination loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _find_suitable_agents(self, 
                                   required_capabilities: List[AgentCapability],
                                   preferred_agent_id: Optional[UUID] = None) -> List[UUID]:
        """Find agents suitable for a task."""
        if preferred_agent_id:
            agent_info = await self.registry.get_agent_info(preferred_agent_id)
            if agent_info and agent_info.status == AgentStatus.IDLE:
                return [preferred_agent_id]
        
        return await self.registry.find_available_agents(
            capabilities=required_capabilities,
            exclude_busy=True
        )
    
    async def _select_agent(self, suitable_agents: List[UUID]) -> Optional[UUID]:
        """Select an agent based on coordination strategy."""
        if not suitable_agents:
            return None
        
        if self.coordination_strategy == CoordinationStrategy.ROUND_ROBIN:
            return await self._select_round_robin(suitable_agents)
        elif self.coordination_strategy == CoordinationStrategy.PRIORITY:
            return await self._select_by_priority(suitable_agents)
        elif self.coordination_strategy == CoordinationStrategy.LOAD_BALANCED:
            return await self._select_load_balanced(suitable_agents)
        else:
            return suitable_agents[0]  # Default to first available
    
    async def _select_round_robin(self, agents: List[UUID]) -> UUID:
        """Select agent using round-robin strategy."""
        if self._last_assigned_agent_index >= len(agents):
            self._last_assigned_agent_index = 0
        
        selected = agents[self._last_assigned_agent_index]
        self._last_assigned_agent_index = (self._last_assigned_agent_index + 1) % len(agents)
        return selected
    
    async def _select_by_priority(self, agents: List[UUID]) -> UUID:
        """Select agent with highest priority."""
        best_agent = None
        best_priority = -1
        
        for agent_id in agents:
            agent_info = await self.registry.get_agent_info(agent_id)
            if agent_info and agent_info.priority > best_priority:
                best_priority = agent_info.priority
                best_agent = agent_id
        
        return best_agent or agents[0]
    
    async def _select_load_balanced(self, agents: List[UUID]) -> UUID:
        """Select agent with least current load."""
        best_agent = None
        min_tasks = float('inf')
        
        for agent_id in agents:
            current_tasks = len(self._agent_tasks.get(agent_id, set()))
            if current_tasks < min_tasks:
                min_tasks = current_tasks
                best_agent = agent_id
        
        return best_agent or agents[0]

    async def _assign_task_to_agent(self, task: Task, agent_id: UUID) -> None:
        """Assign a task to an agent."""
        self._active_tasks[task.id] = task
        self._task_assignments[task.id] = agent_id

        if agent_id not in self._agent_tasks:
            self._agent_tasks[agent_id] = set()
        self._agent_tasks[agent_id].add(task.id)

        # Update coordination context
        if task.id not in self.coordination_context.pending_tasks:
            self.coordination_context.pending_tasks.append(task.id)
        self.coordination_context.task_assignments[task.id] = agent_id

    async def _estimate_completion_time(self, task: Task, agent_id: UUID) -> float:
        """Estimate task completion time."""
        agent_info = await self.registry.get_agent_info(agent_id)
        if agent_info and agent_info.metrics.average_execution_time > 0:
            return agent_info.metrics.average_execution_time
        return 60.0  # Default 1 minute estimate

    async def _update_agent_metrics(self, agent_id: UUID, result: TaskResult) -> None:
        """Update agent metrics after task completion."""
        agent_info = await self.registry.get_agent_info(agent_id)
        if not agent_info:
            return

        metrics = agent_info.metrics

        if result.status == TaskStatus.COMPLETED:
            metrics.tasks_completed += 1
        else:
            metrics.tasks_failed += 1

        # Update execution time metrics
        if result.execution_time:
            total_tasks = metrics.tasks_completed + metrics.tasks_failed
            if total_tasks > 1:
                metrics.average_execution_time = (
                    (metrics.average_execution_time * (total_tasks - 1) + result.execution_time) / total_tasks
                )
            else:
                metrics.average_execution_time = result.execution_time

            metrics.total_execution_time += result.execution_time

        # Update success rate
        total_tasks = metrics.tasks_completed + metrics.tasks_failed
        if total_tasks > 0:
            metrics.success_rate = metrics.tasks_completed / total_tasks

        metrics.last_activity = datetime.now()

        await self.registry.update_agent_metrics(agent_id, metrics)

    async def _process_messages(self) -> None:
        """Process pending messages."""
        try:
            while not self._message_queue.empty():
                message = await asyncio.wait_for(self._message_queue.get(), timeout=0.1)
                await self.send_message(message)
        except asyncio.TimeoutError:
            pass

    async def _process_conflict_resolutions(self) -> None:
        """Process pending conflict resolution requests."""
        try:
            while not self._conflict_resolution_queue.empty():
                request = await asyncio.wait_for(self._conflict_resolution_queue.get(), timeout=0.1)
                result = await self.resolve_conflict(request)
                self.logger.info(f"Resolved conflict {request.conflict_id}: {result.resolution}")
        except asyncio.TimeoutError:
            pass

    async def _update_coordination_context(self) -> None:
        """Update the coordination context."""
        all_agents = await self.registry.get_all_agents()
        self.coordination_context.active_agents = [
            agent_id for agent_id, agent_info in all_agents.items()
            if agent_info.status in [AgentStatus.IDLE, AgentStatus.BUSY]
        ]
        self.coordination_context.last_coordination = datetime.now()

    async def _resolve_by_priority(self, request: ConflictResolutionRequest) -> ConflictResolutionResult:
        """Resolve conflict by agent priority."""
        highest_priority = -1
        winning_agent = None

        for agent_id in request.conflicting_agents:
            agent_info = await self.registry.get_agent_info(agent_id)
            if agent_info and agent_info.priority > highest_priority:
                highest_priority = agent_info.priority
                winning_agent = agent_id

        return ConflictResolutionResult(
            conflict_id=request.conflict_id,
            resolution="priority_based",
            winning_agent_id=winning_agent,
            resolution_data={"winning_priority": highest_priority}
        )

    async def _resolve_by_voting(self, request: ConflictResolutionRequest) -> ConflictResolutionResult:
        """Resolve conflict by voting among all agents."""
        # Simplified voting - in practice, this would involve actual agent voting
        all_agents = await self.registry.get_all_agents()
        eligible_voters = [
            agent_id for agent_id in all_agents.keys()
            if agent_id not in request.conflicting_agents
        ]

        if not eligible_voters:
            # Fallback to priority if no voters
            return await self._resolve_by_priority(request)

        # For now, randomly select from conflicting agents
        # In practice, this would involve actual voting mechanism
        import random
        winning_agent = random.choice(request.conflicting_agents)

        return ConflictResolutionResult(
            conflict_id=request.conflict_id,
            resolution="voting",
            winning_agent_id=winning_agent,
            resolution_data={"voters": len(eligible_voters)}
        )

    async def _resolve_by_consensus(self, request: ConflictResolutionRequest) -> ConflictResolutionResult:
        """Resolve conflict by consensus."""
        # Simplified consensus - in practice, this would involve negotiation
        return ConflictResolutionResult(
            conflict_id=request.conflict_id,
            resolution="consensus",
            resolution_data={"method": "simplified_consensus"}
        )

    async def _resolve_first_wins(self, request: ConflictResolutionRequest) -> ConflictResolutionResult:
        """Resolve conflict by first-come-first-served."""
        winning_agent = request.conflicting_agents[0] if request.conflicting_agents else None

        return ConflictResolutionResult(
            conflict_id=request.conflict_id,
            resolution="first_wins",
            winning_agent_id=winning_agent
        )
