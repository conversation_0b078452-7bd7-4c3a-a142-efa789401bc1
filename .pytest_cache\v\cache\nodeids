["tests/test_config.py::TestCacheConfig::test_default_cache_config", "tests/test_config.py::TestCacheConfig::test_redis_cache_config", "tests/test_config.py::TestExecutionConfig::test_custom_execution_config", "tests/test_config.py::TestExecutionConfig::test_default_execution_config", "tests/test_config.py::TestFrameworkConfig::test_config_custom_settings", "tests/test_config.py::TestFrameworkConfig::test_config_from_env", "tests/test_config.py::TestFrameworkConfig::test_config_from_env_debug_zero", "tests/test_config.py::TestFrameworkConfig::test_config_from_env_false_debug", "tests/test_config.py::TestFrameworkConfig::test_config_from_file_json", "tests/test_config.py::TestFrameworkConfig::test_config_from_file_nonexistent", "tests/test_config.py::TestFrameworkConfig::test_config_from_file_unsupported_format", "tests/test_config.py::TestFrameworkConfig::test_config_to_file_json", "tests/test_config.py::TestFrameworkConfig::test_config_validation_missing_api_key", "tests/test_config.py::TestFrameworkConfig::test_config_validation_nonexistent_plugin_dir", "tests/test_config.py::TestFrameworkConfig::test_config_validation_redis_without_url", "tests/test_config.py::TestFrameworkConfig::test_config_validation_success", "tests/test_config.py::TestFrameworkConfig::test_custom_framework_config", "tests/test_config.py::TestFrameworkConfig::test_default_framework_config", "tests/test_config.py::TestModelConfig::test_custom_model_config", "tests/test_config.py::TestModelConfig::test_default_model_config", "tests/test_config.py::TestOtherConfigs::test_context_config", "tests/test_config.py::TestOtherConfigs::test_logging_config", "tests/test_config.py::TestOtherConfigs::test_monitoring_config", "tests/test_config.py::TestOtherConfigs::test_security_config", "tests/test_config.py::TestPluginConfig::test_custom_plugin_config", "tests/test_config.py::TestPluginConfig::test_default_plugin_config", "tests/test_executor.py::TestTaskExecutor::test_cancel_task", "tests/test_executor.py::TestTaskExecutor::test_concurrent_task_execution", "tests/test_executor.py::TestTaskExecutor::test_different_task_types", "tests/test_executor.py::TestTaskExecutor::test_execute_code_analysis_task", "tests/test_executor.py::TestTaskExecutor::test_execute_simple_task", "tests/test_executor.py::TestTaskExecutor::test_executor_initialization", "tests/test_executor.py::TestTaskExecutor::test_executor_shutdown", "tests/test_executor.py::TestTaskExecutor::test_get_active_task_count", "tests/test_executor.py::TestTaskExecutor::test_get_queue_size", "tests/test_executor.py::TestTaskExecutor::test_task_error_handling", "tests/test_executor.py::TestTaskExecutor::test_task_metadata_preservation", "tests/test_executor.py::TestTaskExecutor::test_task_priority_ordering", "tests/test_executor.py::TestTaskExecutor::test_task_retry_logic", "tests/test_executor.py::TestTaskExecutor::test_task_timeout", "tests/test_integration.py::TestIntegration::test_basic_task_execution_flow", "tests/test_integration.py::TestIntegration::test_concurrent_task_execution", "tests/test_integration.py::TestIntegration::test_configuration_validation", "tests/test_integration.py::TestIntegration::test_error_handling_integration", "tests/test_integration.py::TestIntegration::test_plugin_system_integration", "tests/test_integration.py::TestIntegration::test_system_lifecycle", "tests/test_orchestrator.py::TestAgentOrchestrator::test_execute_task_failure", "tests/test_orchestrator.py::TestAgentOrchestrator::test_execute_task_no_executor", "tests/test_orchestrator.py::TestAgentOrchestrator::test_execute_task_not_initialized", "tests/test_orchestrator.py::TestAgentOrchestrator::test_execute_task_success", "tests/test_orchestrator.py::TestAgentOrchestrator::test_get_component_status", "tests/test_orchestrator.py::TestAgentOrchestrator::test_get_context", "tests/test_orchestrator.py::TestAgentOrchestrator::test_get_context_no_manager", "tests/test_orchestrator.py::TestAgentOrchestrator::test_get_metrics", "tests/test_orchestrator.py::TestAgentOrchestrator::test_get_metrics_no_components", "tests/test_orchestrator.py::TestAgentOrchestrator::test_load_plugin", "tests/test_orchestrator.py::TestAgentOrchestrator::test_load_plugin_no_manager", "tests/test_orchestrator.py::TestAgentOrchestrator::test_orchestrator_initialization", "tests/test_orchestrator.py::TestAgentOrchestrator::test_orchestrator_initialization_failure", "tests/test_orchestrator.py::TestAgentOrchestrator::test_properties", "tests/test_orchestrator.py::TestAgentOrchestrator::test_run_agent_task", "tests/test_orchestrator.py::TestAgentOrchestrator::test_run_agent_task_failure", "tests/test_orchestrator.py::TestAgentOrchestrator::test_run_agent_task_no_agent", "tests/test_orchestrator.py::TestAgentOrchestrator::test_run_agent_task_no_messages", "tests/test_orchestrator.py::TestAgentOrchestrator::test_shutdown", "tests/test_orchestrator.py::TestAgentOrchestrator::test_shutdown_not_initialized", "tests/test_orchestrator.py::TestAgentOrchestrator::test_shutdown_with_errors", "tests/test_plugins.py::TestPluginLoader::test_discover_plugins", "tests/test_plugins.py::TestPluginLoader::test_load_plugin", "tests/test_plugins.py::TestPluginLoader::test_loader_initialization", "tests/test_plugins.py::TestPluginLoader::test_reload_plugin", "tests/test_plugins.py::TestPluginLoader::test_unload_plugin", "tests/test_plugins.py::TestPluginManager::test_execute_plugin_request", "tests/test_plugins.py::TestPluginManager::test_find_plugins_by_capability", "tests/test_plugins.py::TestPluginManager::test_get_plugin_capabilities", "tests/test_plugins.py::TestPluginManager::test_get_plugin_status", "tests/test_plugins.py::TestPluginManager::test_load_plugin_through_manager", "tests/test_plugins.py::TestPluginManager::test_manager_initialization", "tests/test_plugins.py::TestPluginManager::test_plugin_discovery", "tests/test_plugins.py::TestPluginRegistry::test_capability_indexing", "tests/test_plugins.py::TestPluginRegistry::test_dependency_management", "tests/test_plugins.py::TestPluginRegistry::test_dependency_ordering", "tests/test_plugins.py::TestPluginRegistry::test_enable_disable_plugin", "tests/test_plugins.py::TestPluginRegistry::test_register_duplicate_plugin", "tests/test_plugins.py::TestPluginRegistry::test_register_plugin", "tests/test_plugins.py::TestPluginRegistry::test_registry_initialization", "tests/test_plugins.py::TestPluginRegistry::test_unregister_plugin"]