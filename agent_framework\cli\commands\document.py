"""
Document command for the CLI.

Provides documentation generation capabilities including docstrings,
API documentation, and code comments.
"""

import argparse
from typing import Any, Dict

from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator
from .base import (
    AsyncCommandBase, FileInputMixin, OutputFormatMixin, 
    PluginCommandMixin, ValidationMixin
)


class DocumentCommand(AsyncCommandBase, FileInputMixin, OutputFormatMixin, 
                     PluginCommandMixin, ValidationMixin):
    """Command for generating documentation."""
    
    @property
    def name(self) -> str:
        """Get command name."""
        return "document"
    
    @property
    def description(self) -> str:
        """Get command description."""
        return "Generate documentation for code"
    
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments."""
        # Documentation type subcommands
        subparsers = parser.add_subparsers(
            dest='doc_type',
            help='Type of documentation to generate',
            metavar='TYPE'
        )
        
        # Docstring generation
        docstring_parser = subparsers.add_parser(
            'docstrings',
            help='Generate docstrings for functions and classes'
        )
        self._add_docstring_arguments(docstring_parser)
        
        # API documentation
        api_parser = subparsers.add_parser(
            'api',
            help='Generate API documentation'
        )
        self._add_api_arguments(api_parser)
        
        # Code comments
        comments_parser = subparsers.add_parser(
            'comments',
            help='Generate inline code comments'
        )
        self._add_comments_arguments(comments_parser)
        
        # README generation
        readme_parser = subparsers.add_parser(
            'readme',
            help='Generate README file'
        )
        self._add_readme_arguments(readme_parser)
        
        # Plugin arguments (add to main parser)
        self.add_plugin_arguments(parser)
        
        # Output arguments (add to main parser)
        self.add_output_arguments(parser)
    
    def _add_docstring_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add docstring generation arguments."""
        # File input
        self.add_file_arguments(parser)
        
        # Docstring options
        parser.add_argument(
            '--style',
            choices=['google', 'numpy', 'sphinx'],
            default='google',
            help='Docstring style (default: google)'
        )
        parser.add_argument(
            '--include-examples',
            action='store_true',
            help='Include usage examples in docstrings'
        )
        parser.add_argument(
            '--overwrite',
            action='store_true',
            help='Overwrite existing docstrings'
        )
    
    def _add_api_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add API documentation arguments."""
        # File input
        self.add_file_arguments(parser)
        
        # API doc options
        parser.add_argument(
            '--format', '-f',
            choices=['markdown', 'rst', 'html'],
            default='markdown',
            help='Documentation format (default: markdown)'
        )
        parser.add_argument(
            '--include-private',
            action='store_true',
            help='Include private methods and classes'
        )
        parser.add_argument(
            '--with-toc',
            action='store_true',
            help='Generate table of contents'
        )
    
    def _add_comments_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add code comments arguments."""
        # File input
        self.add_file_arguments(parser)
        
        # Comment options
        parser.add_argument(
            '--style',
            choices=['inline', 'block'],
            default='inline',
            help='Comment style (default: inline)'
        )
        parser.add_argument(
            '--complexity-threshold',
            type=int,
            default=5,
            help='Complexity threshold for adding comments (default: 5)'
        )
    
    def _add_readme_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add README generation arguments."""
        parser.add_argument(
            '--project-name', '-n',
            required=True,
            help='Project name'
        )
        parser.add_argument(
            '--description', '-d',
            required=True,
            help='Project description'
        )
        parser.add_argument(
            '--features', '-f',
            nargs='*',
            help='Project features'
        )
        parser.add_argument(
            '--installation', '-i',
            help='Installation instructions'
        )
        parser.add_argument(
            '--usage-example', '-u',
            help='Usage example code'
        )
    
    def get_help_text(self) -> str:
        """Get detailed help text."""
        return """
Generate various types of documentation for Python code.

Documentation Types:
  docstrings    - Generate docstrings for functions and classes
  api           - Generate API documentation
  comments      - Generate inline code comments
  readme        - Generate README file for project

Examples:
  agent-framework document docstrings --file mymodule.py --style google
  agent-framework document api --file mypackage.py --format markdown
  agent-framework document comments --file complex_code.py --style inline
  agent-framework document readme --project-name "My Project" --description "A cool project"
        """
    
    async def execute(self, args: argparse.Namespace, 
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the document command."""
        try:
            # Handle plugin listing
            if args.list_plugins:
                return await self.list_plugins(orchestrator)
            
            # Check if documentation type is specified
            if not args.doc_type:
                return {
                    "success": False,
                    "error": "No documentation type specified. Use --help for available types."
                }
            
            # Execute specific documentation generation
            if args.doc_type == 'docstrings':
                result = await self._generate_docstrings(args, orchestrator)
            elif args.doc_type == 'api':
                result = await self._generate_api_docs(args, orchestrator)
            elif args.doc_type == 'comments':
                result = await self._generate_comments(args, orchestrator)
            elif args.doc_type == 'readme':
                result = await self._generate_readme(args, orchestrator)
            else:
                return {
                    "success": False,
                    "error": f"Unknown documentation type: {args.doc_type}"
                }
            
            # Format and output results
            if result.get('success'):
                formatted_output = self._format_documentation_results(result, args)
                self.write_output(formatted_output, args.output)
                
                return {
                    "success": True,
                    "message": f"Successfully generated {args.doc_type} documentation",
                    "result": result
                }
            else:
                return result
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _generate_docstrings(self, args: argparse.Namespace,
                                  orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Generate docstrings."""
        # Get code input
        code = self.get_code_input(args)
        
        # Generate docstrings
        request_params = {
            "code": code,
            "style": args.style,
            "include_examples": args.include_examples
        }
        
        return await self.handle_plugin_request(
            orchestrator,
            'documentation',
            'generate_docstrings',
            request_params
        )
    
    async def _generate_api_docs(self, args: argparse.Namespace,
                                orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Generate API documentation."""
        # Get code input
        code = self.get_code_input(args)
        
        # Generate API docs
        request_params = {
            "code": code,
            "format": args.format,
            "include_private": args.include_private
        }
        
        return await self.handle_plugin_request(
            orchestrator,
            'documentation',
            'generate_api_docs',
            request_params
        )
    
    async def _generate_comments(self, args: argparse.Namespace,
                                orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Generate code comments."""
        # Get code input
        code = self.get_code_input(args)
        
        # Generate comments
        request_params = {
            "code": code,
            "comment_style": args.style
        }
        
        return await self.handle_plugin_request(
            orchestrator,
            'documentation',
            'generate_comments',
            request_params
        )
    
    async def _generate_readme(self, args: argparse.Namespace,
                              orchestrator: AgentOrchestrator) -> Dict[str, Any]:
        """Generate README file."""
        # Prepare README parameters
        request_params = {
            "project_name": args.project_name,
            "description": args.description
        }
        
        if args.features:
            request_params["features"] = args.features
        
        if args.installation:
            request_params["installation"] = args.installation
        
        if args.usage_example:
            request_params["usage_example"] = args.usage_example
        
        return await self.handle_plugin_request(
            orchestrator,
            'documentation',
            'generate_readme',
            request_params
        )
    
    def _format_documentation_results(self, result: Dict[str, Any], 
                                     args: argparse.Namespace) -> str:
        """Format documentation results for output."""
        if not result.get('success'):
            return f"Documentation generation failed: {result.get('error', 'Unknown error')}"
        
        data = result.get('result', {})
        
        if args.format == 'json':
            return self.format_output(data, 'json', args.pretty)
        elif args.format == 'yaml':
            return self.format_output(data, 'yaml', args.pretty)
        else:
            # Text format - show generated documentation
            if args.doc_type == 'docstrings':
                if 'documented_code' in data:
                    return data['documented_code']
                else:
                    return "No documented code generated"
            
            elif args.doc_type == 'api':
                output = []
                if 'toc' in data and args.with_toc:
                    output.append(data['toc'])
                    output.append("")
                
                if 'documentation' in data:
                    output.append(data['documentation'])
                
                return "\n".join(output) if output else "No API documentation generated"
            
            elif args.doc_type == 'comments':
                if 'commented_code' in data:
                    return data['commented_code']
                else:
                    return "No commented code generated"
            
            elif args.doc_type == 'readme':
                if 'readme_content' in data:
                    return data['readme_content']
                else:
                    return "No README content generated"
            
            else:
                return str(data)
