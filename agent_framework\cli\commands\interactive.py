"""
Interactive command for the CLI.

Provides an interactive mode for the agent framework.
"""

import argparse
from typing import Any, Dict

from ...core.config import FrameworkConfig
from ...core.orchestrator import AgentOrchestrator
from .base import AsyncCommandBase


class InteractiveCommand(AsyncCommandBase):
    """Command for starting interactive mode."""
    
    @property
    def name(self) -> str:
        """Get command name."""
        return "interactive"
    
    @property
    def description(self) -> str:
        """Get command description."""
        return "Start interactive mode"
    
    def add_arguments(self, parser: argparse.ArgumentParser) -> None:
        """Add command-specific arguments."""
        parser.add_argument(
            '--no-banner',
            action='store_true',
            help='Skip the welcome banner'
        )
        parser.add_argument(
            '--history-file',
            type=str,
            help='Path to command history file'
        )
    
    def get_help_text(self) -> str:
        """Get detailed help text."""
        return """
Start an interactive session with the agent framework.

In interactive mode, you can:
  - Ask questions about code
  - Request code analysis and optimization
  - Generate code interactively
  - Get debugging assistance
  - Access all framework capabilities

Examples:
  agent-framework interactive
  agent-framework interactive --no-banner
        """
    
    async def execute(self, args: argparse.Namespace, 
                     orchestrator: AgentOrchestrator,
                     config: FrameworkConfig) -> Dict[str, Any]:
        """Execute the interactive command."""
        from ..interactive import InteractiveCLI
        
        interactive_cli = InteractiveCLI(
            orchestrator, 
            config,
            show_banner=not args.no_banner,
            history_file=args.history_file
        )
        
        exit_code = await interactive_cli.run()
        
        return {
            "success": True,
            "message": "Interactive session ended",
            "exit_code": exit_code
        }
