"""
CLI commands for the agent framework.

Provides command implementations for different agent operations.
"""

from .base import BaseCommand, CommandRegistry
from .analyze import AnalyzeCommand
from .generate import GenerateCommand
from .optimize import OptimizeCommand
from .debug import DebugCommand
from .document import DocumentCommand

__all__ = [
    'BaseCommand',
    'CommandRegistry',
    'AnalyzeCommand',
    'GenerateCommand', 
    'OptimizeCommand',
    'DebugCommand',
    'DocumentCommand'
]
