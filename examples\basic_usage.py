"""
Basic usage example for the Programming Assistant Agent Framework.

This example demonstrates how to:
1. Initialize the framework
2. Load plugins
3. Execute tasks
4. Use the agent for programming assistance
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import agent_framework
sys.path.insert(0, str(Path(__file__).parent.parent))

from agent_framework import AgentOrchestrator, FrameworkConfig, Task, TaskPriority


async def main():
    """Main example function."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    print("🚀 Programming Assistant Agent Framework - Basic Usage Example")
    print("=" * 60)

    # Create configuration
    config = FrameworkConfig()

    # Set API key from environment or use default
    config.model.api_key = os.getenv(
        "AGENT_API_KEY",
        "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"
    )

    # Configure plugin directories
    config.plugins.plugin_directories = ["plugins"]
    config.plugins.auto_load_plugins = True

    # Initialize the orchestrator
    orchestrator = AgentOrchestrator(config)

    try:
        print("\n📦 Initializing framework...")
        await orchestrator.initialize()
        print("✅ Framework initialized successfully!")

        # Get system metrics
        print("\n📊 System Metrics:")
        metrics = await orchestrator.get_metrics()
        print(f"   CPU Usage: {metrics.cpu_usage:.1f}%")
        print(f"   Memory Usage: {metrics.memory_usage:.1f}%")
        print(f"   Active Tasks: {metrics.active_tasks}")
        print(f"   Loaded Plugins: {metrics.plugin_count}")

        # Example 1: Execute a simple task
        print("\n🔧 Example 1: Executing a simple task")
        task = Task(
            name="Hello World Task",
            description="A simple test task",
            task_type="generic",
            priority=TaskPriority.NORMAL,
            parameters={"message": "Hello from the agent framework!"}
        )

        result = await orchestrator.execute_task(task)
        print(f"   Task Status: {result.status.value}")
        print(f"   Result: {result.result}")
        print(f"   Execution Time: {result.execution_time:.3f}s")

        # Example 2: Use the agent for programming assistance
        print("\n🤖 Example 2: Using the agent for programming assistance")

        # Simple code analysis request
        response = await orchestrator.run_agent_task(
            "Analyze this Python code for potential improvements:\n\n"
            "def calculate_sum(numbers):\n"
            "    total = 0\n"
            "    for i in range(len(numbers)):\n"
            "        total = total + numbers[i]\n"
            "    return total\n"
        )

        print("   Agent Response:")
        print(f"   {response}")

        # Example 3: Code analysis task
        print("\n🔍 Example 3: Code analysis task")
        analysis_task = Task(
            name="Code Analysis",
            description="Analyze code complexity",
            task_type="code_analysis",
            priority=TaskPriority.HIGH,
            parameters={
                "code": """
def fibonacci(n):
    if n <= 1:
        return n
    else:
        return fibonacci(n-1) + fibonacci(n-2)

def factorial(n):
    if n == 0:
        return 1
    else:
        return n * factorial(n-1)
"""
            }
        )

        analysis_result = await orchestrator.execute_task(analysis_task)
        print(f"   Analysis Status: {analysis_result.status.value}")
        print(f"   Analysis Result: {analysis_result.result}")

        # Example 4: Get framework status
        print("\n📈 Example 4: Framework status")
        if orchestrator._plugin_manager:
            plugin_status = await orchestrator._plugin_manager.get_plugin_status()
            print(f"   Total Plugins: {plugin_status['registry_stats']['total_plugins']}")
            print(f"   Loaded Plugins: {plugin_status['loaded_count']}")
            print(f"   Enabled Plugins: {plugin_status['registry_stats']['enabled_plugins']}")

        print("\n✨ Examples completed successfully!")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

    finally:
        print("\n🔄 Shutting down framework...")
        await orchestrator.shutdown()
        print("✅ Framework shutdown complete!")


if __name__ == "__main__":
    asyncio.run(main())